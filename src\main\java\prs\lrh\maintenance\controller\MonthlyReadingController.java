package prs.lrh.maintenance.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import prs.lrh.maintenance.model.HouseRecord;
import prs.lrh.maintenance.model.MonthlyReading;
import prs.lrh.maintenance.model.WaterReading;
import prs.lrh.maintenance.model.WaterReadingForm;
import prs.lrh.maintenance.repository.HouseRepository;
import prs.lrh.maintenance.repository.MonthlyReadingRepository;

import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.Map;
import java.util.HashMap;

@Controller
@RequestMapping("/readings")
public class MonthlyReadingController {

    @Autowired
    private MonthlyReadingRepository monthlyReadingRepository;
    
    @Autowired
    private HouseRepository houseRepository;

    @GetMapping
    public String listReadings(Model model, 
                              @RequestParam(required = false) Integer month,
                              @RequestParam(required = false) Integer year) {
        
        List<MonthlyReading> readings;
        
        // If both month and year are provided
        if (month != null && year != null) {
            readings = monthlyReadingRepository.findByMonthAndYear(month, year);
            
            // Check if this is current or future month and no readings exist
            YearMonth requestedYearMonth = YearMonth.of(year, month);
            YearMonth currentYearMonth = YearMonth.now();
            
            if (readings.isEmpty() && (requestedYearMonth.equals(currentYearMonth) || requestedYearMonth.isAfter(currentYearMonth))) {
                // Create a new monthly reading with water readings for all houses
                MonthlyReading newReading = createNewMonthlyReading(month, year);
                readings = List.of(newReading);
            }
        } 
        // If only year is provided
        else if (year != null) {
            readings = monthlyReadingRepository.findByYear(year);
        } 
        // If no filters are provided
        else {
            readings = monthlyReadingRepository.findByOrderByYearDescMonthDesc();
        }
        
        model.addAttribute("readings", readings);
        return "readings";
    }
    
    @GetMapping("/{id}")
    public String viewReading(@PathVariable String id, Model model) {
        Optional<MonthlyReading> reading = monthlyReadingRepository.findById(id);
        
        if (reading.isPresent()) {
            MonthlyReading monthlyReading = reading.get();
            model.addAttribute("reading", monthlyReading);
            
            // Add the form-backing object
            WaterReadingForm form = new WaterReadingForm(monthlyReading.getWaterReadings());
            model.addAttribute("waterReadingForm", form);
            
            return "reading-detail";
        } else {
            return "redirect:/readings";
        }
    }
    
    private MonthlyReading createNewMonthlyReading(int month, int year) {
        // Create a new monthly reading
        MonthlyReading reading = new MonthlyReading();
        reading.setMonth(month);
        reading.setYear(year);
        reading.setCreatedAt(LocalDateTime.now());
        reading.setModifiedAt(LocalDateTime.now());
        
        // Get all houses sorted by ID
        List<HouseRecord> houses = houseRepository.findAllByOrderByIdAsc();
        
        // Create water readings for each house
        List<WaterReading> waterReadings = houses.stream()
            .map(house -> {
                WaterReading waterReading = new WaterReading();
                waterReading.setHouseId(house.getId());
                waterReading.setHouseNumber(house.getHouseNumber());
                waterReading.setPrvReading(house.getCurrentWaterMeterReading());
                waterReading.setCurrentReading(house.getCurrentWaterMeterReading()); // Default to same as previous
                waterReading.setUnits(0);; // Default to 0
                return waterReading;
            })
            .collect(Collectors.toList());
        
        reading.setWaterReadings(waterReadings);
        
        // Save the new reading
        MonthlyReading saved = monthlyReadingRepository.save(reading);
        return saved;
    }

    @PostMapping("/update")
    public String updateReading(@RequestParam String readingId,
                               @RequestParam int month,
                               @RequestParam int year,
                               @ModelAttribute WaterReadingForm form) {
        
        Optional<MonthlyReading> optionalReading = monthlyReadingRepository.findById(readingId);
        
        if (optionalReading.isPresent() && form.getWaterReadings() != null) {
            MonthlyReading reading = optionalReading.get();
            
            // Create a map of updated readings by house ID for easier lookup
            Map<Long, WaterReading> updatedReadingsMap = new HashMap<>();
            for (WaterReading updatedReading : form.getWaterReadings()) {
                updatedReadingsMap.put(updatedReading.getHouseId(), updatedReading);
            }
            
            // Update existing readings with the values from the form
            List<WaterReading> existingReadings = reading.getWaterReadings();
            for (WaterReading existingReading : existingReadings) {
                WaterReading updatedReading = updatedReadingsMap.get(existingReading.getHouseId());
                if (updatedReading != null) {
                    existingReading.setCurrentReading(updatedReading.getCurrentReading());
                    existingReading.setUnits(updatedReading.getUnits());
                }
            }
            
            // Update modified timestamp
            reading.setModifiedAt(LocalDateTime.now());
            
            // Save updated reading
            monthlyReadingRepository.save(reading);
            
            // Update house records if month and year are current
            YearMonth readingYearMonth = YearMonth.of(year, month);
            YearMonth currentYearMonth = YearMonth.now();
            
            if (readingYearMonth.equals(currentYearMonth)) {
                List<HouseRecord> houses = houseRepository.findAll();
                
                for (HouseRecord house : houses) {
                    for (WaterReading waterReading : reading.getWaterReadings()) {
                        if (house.getId().equals(waterReading.getHouseId()) && 
                            house.getCurrentWaterMeterReading() != waterReading.getCurrentReading()) {
                            
                            house.setPreviousWaterMeterReading(waterReading.getPrvReading());
                            house.setCurrentWaterMeterReading(waterReading.getCurrentReading());
                            houseRepository.save(house);
                            break;
                        }
                    }
                }
            }
        }
        
        return "redirect:/readings/" + readingId;
    }
}










