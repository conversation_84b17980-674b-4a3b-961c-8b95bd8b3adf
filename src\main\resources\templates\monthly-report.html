<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:replace="~{fragments/layout_2 :: head('Monthly Maintenance Report')}"></head>
<body>
    <div th:replace="~{fragments/layout_2 :: header}"></div>
    <div class="container mt-4">
        <h2>Monthly Maintenance Report</h2>
        
        <div class="card mb-4">
            <div class="card-header">
                <h4 th:text="${report.month == 1 ? 'January' : 
                              report.month == 2 ? 'February' : 
                              report.month == 3 ? 'March' : 
                              report.month == 4 ? 'April' : 
                              report.month == 5 ? 'May' : 
                              report.month == 6 ? 'June' : 
                              report.month == 7 ? 'July' : 
                              report.month == 8 ? 'August' : 
                              report.month == 9 ? 'September' : 
                              report.month == 10 ? 'October' : 
                              report.month == 11 ? 'November' : 'December'} + ' ' + ${report.year}"></h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <h5 class="card-title">Total Billed</h5>
                                <h4 class="card-text" th:text="${'Rs.' + #numbers.formatDecimal(report.totalBilled, 1, 'COMMA', 2, 'POINT')}"></h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <h5 class="card-title">Total Paid</h5>
                                <h4 class="card-text" th:text="${'Rs.' + #numbers.formatDecimal(report.totalPaid, 1, 'COMMA', 2, 'POINT')}"></h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-danger text-white">
                            <div class="card-body">
                                <h5 class="card-title">Outstanding</h5>
                                <h4 class="card-text" th:text="${'Rs.' + #numbers.formatDecimal(report.totalOutstanding, 1, 'COMMA', 2, 'POINT')}"></h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="form-group">
            <form th:action="@{/reports/monthly}" method="get" class="form-inline">
                <select name="month" class="form-control mr-2">
                    <option value="1" th:selected="${currentMonth == 1}">January</option>
                    <option value="2" th:selected="${currentMonth == 2}">February</option>
                    <option value="3" th:selected="${currentMonth == 3}">March</option>
                    <option value="4" th:selected="${currentMonth == 4}">April</option>
                    <option value="5" th:selected="${currentMonth == 5}">May</option>
                    <option value="6" th:selected="${currentMonth == 6}">June</option>
                    <option value="7" th:selected="${currentMonth == 7}">July</option>
                    <option value="8" th:selected="${currentMonth == 8}">August</option>
                    <option value="9" th:selected="${currentMonth == 9}">September</option>
                    <option value="10" th:selected="${currentMonth == 10}">October</option>
                    <option value="11" th:selected="${currentMonth == 11}">November</option>
                    <option value="12" th:selected="${currentMonth == 12}">December</option>
                </select>
                <select name="year" class="form-control mr-2">
                    <option th:each="y : ${#numbers.sequence(2020, #dates.year(#dates.createNow()))}" 
                            th:value="${y}" th:text="${y}" th:selected="${y == currentYear}"></option>
                </select>
                <button type="submit" class="btn btn-primary">Generate Report</button>
            </form>
        </div>
        
        <div class="mb-3 d-flex">
            <button id="exportPdfBtn" class="btn btn-secondary mr-2">Export as PDF (Client-side)</button>
            <a th:href="@{/reports/monthly/pdf(month=${currentMonth},year=${currentYear})}" 
               class="btn btn-secondary ml-2">Export as PDF (Server-side)</a>
        </div>
        
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>House Number</th>
                    <th colspan="2">Owner/Resident</th>
                    <th>Fixed Amount</th>
                    <th>Water Bill</th>
                    <th>Other Charges</th>
                    <th>Past Dues</th>
                    <th>Total Billed</th>
                    <th>Amount Paid</th>
                    <th>Balance</th>
                </tr>
            </thead>
            <tbody>
                <tr th:each="item : ${report.items}">
                    <td th:text="${item.houseRecord.houseNumber}"></td>
                    <td th:text="${item.houseRecord.ownerName == item.houseRecord.residentName || item.houseRecord.residentName == null || item.houseRecord.residentName == '' ? item.houseRecord.ownerName : item.houseRecord.ownerName + '/' + item.houseRecord.residentName}" colspan="2"></td>
                    <td th:text="${item.monthlyMaintenanceRecord != null ? 'Rs.' + #numbers.formatDecimal(item.monthlyMaintenanceRecord.fixedAmt, 1, 'COMMA', 2, 'POINT') : '-'}"></td>
                    <td th:text="${item.monthlyMaintenanceRecord != null ? 'Rs.' + #numbers.formatDecimal(item.monthlyMaintenanceRecord.waterBill, 1, 'COMMA', 2, 'POINT') : '-'}"></td>
                    <td th:text="${item.monthlyMaintenanceRecord != null ? 'Rs.' + #numbers.formatDecimal(item.monthlyMaintenanceRecord.otherCharges, 1, 'COMMA', 2, 'POINT') : '-'}"></td>
                    <td th:text="${item.monthlyMaintenanceRecord != null ? 'Rs.' + #numbers.formatDecimal(item.monthlyMaintenanceRecord.pastDues, 1, 'COMMA', 2, 'POINT') : '-'}"></td>
                    <td th:text="${item.monthlyMaintenanceRecord != null ? 'Rs.' + #numbers.formatDecimal(item.monthlyMaintenanceRecord.total, 1, 'COMMA', 2, 'POINT') : '-'}"></td>
                    <td th:text="${item.payments != null && !item.payments.isEmpty() ? 'Rs.' + #numbers.formatDecimal(item.totalAmtPaid, 1, 'COMMA', 2, 'POINT') : '-'}"></td>
                    <td th:text="${item.monthlyMaintenanceRecord != null ? 'Rs.' + #numbers.formatDecimal(item.monthlyMaintenanceRecord.total - item.totalAmtPaid, 1, 'COMMA', 2, 'POINT') : '-'}"></td>
                </tr>
            </tbody>
        </table>
    </div>
    <div th:replace="~{fragments/layout_2 :: footer}"></div>
    <script>
        document.getElementById('exportPdfBtn').addEventListener('click', function() {
            // Create PDF document
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF();
            
            // Add title
            const month = document.querySelector('.card-header h4').textContent;
            doc.setFont("helvetica", "normal");
            doc.setFontSize(18);
            doc.text('Monthly Maintenance Report', 14, 20);
            doc.setFontSize(14);
            doc.text(month, 14, 30);
            
            // Add summary data
            const totalBilled = document.querySelector('.bg-primary .card-text').textContent;
            const totalPaid = document.querySelector('.bg-success .card-text').textContent;
            const totalOutstanding = document.querySelector('.bg-danger .card-text').textContent;
            
            doc.setFontSize(12);
            doc.text(`Total Billed: ${totalBilled}`, 14, 45);
            doc.text(`Total Paid: ${totalPaid}`, 14, 55);
            doc.text(`Outstanding: ${totalOutstanding}`, 14, 65);
            
            // Create table data from the HTML table
            const table = document.querySelector('.table');
            const headers = Array.from(table.querySelectorAll('thead th')).map(th => th.textContent);
            
            const rows = Array.from(table.querySelectorAll('tbody tr')).map(tr => {
                return Array.from(tr.querySelectorAll('td')).map(td => td.textContent);
            });
            
            // Generate the table in the PDF
            doc.autoTable({
                head: [headers],
                body: rows,
                startY: 75,
                theme: 'grid',
                styles: {
                    fontSize: 8,
                    cellPadding: 2
                },
                headStyles: {
                    fillColor: [66, 139, 202],
                    textColor: 255
                },
                alternateRowStyles: {
                    fillColor: [240, 240, 240]
                }
            });
            
            // Add footer with date
            const now = new Date();
            const footer = `Generated on ${now.toLocaleDateString()} at ${now.toLocaleTimeString()}`;
            const pageCount = doc.internal.getNumberOfPages();
            
            for (let i = 1; i <= pageCount; i++) {
                doc.setPage(i);
                doc.setFontSize(8);
                doc.text(footer, 14, doc.internal.pageSize.height - 10);
                doc.text(`Page ${i} of ${pageCount}`, doc.internal.pageSize.width - 30, doc.internal.pageSize.height - 10);
            }
            
            // Save the PDF
            doc.save(`Maintenance_Report_${month.replace(/\s+/g, '_')}.pdf`);
        });
    </script>
</body>
</html>
