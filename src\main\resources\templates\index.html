<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:replace="~{fragments/layout :: head('Lake Ridge Homes')}">
</head>
<body>
    <div th:replace="~{fragments/layout :: header}"></div>
    
    <div class="container mt-5">
        <div class="jumbotron bg-light p-5 rounded">
            <h1 class="display-4">Welcome to Lake Ridge Homes</h1>
            <p class="lead">A comprehensive management system for residential community administration.</p>
            <hr class="my-4">
            <p>Manage expenses, track payments, monitor maintenance, and generate reports with ease.</p>
        </div>
        
        <div class="row mt-5">
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-house-door"></i> House Management</h5>
                    </div>
                    <div class="card-body">
                        <p>Track and manage all properties within the community.</p>
                        <ul>
                            <li>Maintain house details and ownership information</li>
                            <li>Track water meter readings</li>
                            <li>Monitor payment balances</li>
                        </ul>
                        <a th:href="@{/houses}" class="btn btn-outline-primary">Manage Houses</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="bi bi-cash-coin"></i> Payment Tracking</h5>
                    </div>
                    <div class="card-body">
                        <p>Record and monitor all payments from residents.</p>
                        <ul>
                            <li>Track monthly maintenance payments</li>
                            <li>Generate payment receipts</li>
                            <li>View payment history by house</li>
                        </ul>
                        <a th:href="@{/payments}" class="btn btn-outline-success">Manage Payments</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0"><i class="bi bi-receipt"></i> Expense Management</h5>
                    </div>
                    <div class="card-body">
                        <p>Record and categorize all community expenses.</p>
                        <ul>
                            <li>Track monthly expenses by category</li>
                            <li>Generate expense reports</li>
                            <li>Analyze spending patterns</li>
                        </ul>
                        <a th:href="@{/expenses}" class="btn btn-outline-danger">Manage Expenses</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="bi bi-droplet"></i> Water Reading Management</h5>
                    </div>
                    <div class="card-body">
                        <p>Track water consumption for each property.</p>
                        <ul>
                            <li>Record monthly meter readings</li>
                            <li>Calculate water usage</li>
                            <li>Generate water billing reports</li>
                        </ul>
                        <a th:href="@{/readings}" class="btn btn-outline-info">Manage Readings</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0"><i class="bi bi-tools"></i> Maintenance Tracking</h5>
                    </div>
                    <div class="card-body">
                        <p>Manage community maintenance tasks and repairs.</p>
                        <ul>
                            <li>Log maintenance requests</li>
                            <li>Track repair status</li>
                            <li>Assign maintenance tasks</li>
                        </ul>
                        <a th:href="@{/maintenance}" class="btn btn-outline-warning">Manage Maintenance</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0"><i class="bi bi-bar-chart"></i> Financial Reports</h5>
                    </div>
                    <div class="card-body">
                        <p>Generate comprehensive financial reports.</p>
                        <ul>
                            <li>Monthly income and expense summaries</li>
                            <li>Balance sheets</li>
                            <li>Payment collection reports</li>
                        </ul>
                        <a th:href="@{/reports/monthly}" class="btn btn-outline-secondary">View Reports</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-dark text-white">
                        <h5 class="mb-0"><i class="bi bi-credit-card"></i> Credit Management</h5>
                    </div>
                    <div class="card-body">
                        <p>Track and manage community credits and income sources.</p>
                        <ul>
                            <li>Record miscellaneous income</li>
                            <li>Track donations and contributions</li>
                            <li>Manage other revenue streams</li>
                        </ul>
                        <a th:href="@{/credits}" class="btn btn-outline-dark">Manage Credits</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-tags"></i> Category Management</h5>
                    </div>
                    <div class="card-body">
                        <p>Manage expense and income categories.</p>
                        <ul>
                            <li>Create and edit expense categories</li>
                            <li>Manage subcategories</li>
                            <li>Organize financial data</li>
                        </ul>
                        <a th:href="@{/categories}" class="btn btn-outline-primary">Manage Categories</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="bi bi-wallet2"></i> Account Balances</h5>
                    </div>
                    <div class="card-body">
                        <p>Track overall financial health of the community.</p>
                        <ul>
                            <li>Monitor monthly account balances</li>
                            <li>Track income vs. expenses</li>
                            <li>View financial trends over time</li>
                        </ul>
                        <a th:href="@{/account-balances}" class="btn btn-outline-success">View Account Balances</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div th:replace="~{fragments/layout :: footer}"></div>
</body>
</html>

