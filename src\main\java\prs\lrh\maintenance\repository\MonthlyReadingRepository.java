package prs.lrh.maintenance.repository;

import org.springframework.data.mongodb.repository.MongoRepository;
import prs.lrh.maintenance.model.MonthlyReading;

import java.util.List;

public interface MonthlyReadingRepository extends MongoRepository<MonthlyReading, String> {
    List<MonthlyReading> findByMonthAndYear(int month, int year);
    List<MonthlyReading> findByYear(int year);
    List<MonthlyReading> findByOrderByYearDescMonthDesc();
}