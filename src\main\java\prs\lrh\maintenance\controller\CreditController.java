package prs.lrh.maintenance.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import prs.lrh.maintenance.model.CategoryRecord;
import prs.lrh.maintenance.model.CreditItem;
import prs.lrh.maintenance.model.CreditRecord;
import prs.lrh.maintenance.repository.CategoryRepository;
import prs.lrh.maintenance.repository.CreditRepository;

import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Controller
@RequestMapping("/credits")
public class CreditController {

    private static final Logger logger = LoggerFactory.getLogger(CreditController.class);
    
    @Autowired
    private CreditRepository creditRepository;
    
    @Autowired
    private CategoryRepository categoryRepository;
    
    @GetMapping
    public String listCredits(@RequestParam(required = false) Integer month,
                             @RequestParam(required = false) Integer year,
                             Model model) {
        
        List<CreditRecord> credits;
        
        // If both month and year are provided
        if (month != null && year != null) {
            Optional<CreditRecord> creditOpt = creditRepository.findByMonthAndYear(month, year);
            if (creditOpt.isPresent()) {
                credits = List.of(creditOpt.get());
            } else {
                // Check if this is current or future month
                YearMonth requestedYearMonth = YearMonth.of(year, month);
                YearMonth currentYearMonth = YearMonth.now();
                
                if (requestedYearMonth.equals(currentYearMonth) || requestedYearMonth.isAfter(currentYearMonth)) {
                    // Create a new empty credit record for the month
                    CreditRecord newCredit = createNewCreditRecord(month, year);
                    credits = List.of(newCredit);
                } else {
                    credits = new ArrayList<>();
                }
            }
        } 
        // If only year is provided
        else if (year != null) {
            credits = creditRepository.findByYear(year);
        } 
        // If no filters are provided
        else {
            credits = creditRepository.findByOrderByYearDescMonthDesc();
        }
        
        logger.info("Found {} credit records", credits.size());
        model.addAttribute("credits", credits);
        model.addAttribute("currentMonth", YearMonth.now().getMonthValue());
        model.addAttribute("currentYear", YearMonth.now().getYear());
        return "credits";
    }
    
    @GetMapping("/{id}")
    public String viewCredit(@PathVariable String id, Model model) {
        logger.debug("Viewing credit record with id: {}", id);
        Optional<CreditRecord> creditRecord = creditRepository.findById(id);
        
        if (creditRecord.isPresent()) {
            CreditRecord credit = creditRecord.get();
            logger.debug("Credit record found: {}", credit.getId());
            
            // Get credit categories - only get CREDIT_TYPE categories
            List<CategoryRecord> creditCategories = categoryRepository.findByTypeOrderByCategoryAsc("CREDIT_TYPE");
            
            model.addAttribute("credit", credit);
            model.addAttribute("creditCategories", creditCategories);
            
            // Calculate total credit amount
            double totalAmount = credit.getMonthlyCredits().stream()
                .mapToDouble(CreditItem::getAmount)
                .sum();
            
            model.addAttribute("totalAmount", totalAmount);
            
            return "credit-detail";
        } else {
            logger.warn("Credit record not found with id: {}", id);
            return "redirect:/credits";
        }
    }
    
    private CreditRecord createNewCreditRecord(int month, int year) {
        logger.debug("Creating new credit record for {}/{}", month, year);
        
        // Create a new credit record
        CreditRecord credit = new CreditRecord();
        credit.setMonth(month);
        credit.setYear(year);
        credit.setMonthlyCredits(new ArrayList<>());
        credit.setCreatedAt(LocalDateTime.now());
        credit.setModifiedAt(LocalDateTime.now());
        
        // Save the new credit record
        CreditRecord saved = creditRepository.save(credit);
        logger.info("Created new credit record with id: {}", saved.getId());
        return saved;
    }
    
    @PostMapping("/add-item")
    public String addCreditItem(@RequestParam String creditId,
                               @RequestParam double amount,
                               @RequestParam String category,
                               @RequestParam String subCategory,
                               @RequestParam String comments,
                               @RequestParam String creditDate,
                               @RequestParam(required = false) String itemId,
                               @RequestParam(required = false, defaultValue = "false") boolean editMode) {
        
        logger.info("Adding/updating credit item to record: {}", creditId);
        
        Optional<CreditRecord> creditOpt = creditRepository.findById(creditId);
        
        if (creditOpt.isPresent()) {
            CreditRecord credit = creditOpt.get();
            
            // Parse the credit date
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm");
            LocalDateTime parsedCreditDate = LocalDateTime.parse(creditDate, formatter);
            
            if (editMode && itemId != null && !itemId.isEmpty()) {
                // Update existing item
                credit.getMonthlyCredits().stream()
                    .filter(item -> item.getId().equals(itemId))
                    .findFirst()
                    .ifPresent(item -> {
                        item.setAmount(amount);
                        item.setCategory(category);
                        item.setSubCategory(subCategory);
                        item.setComments(comments);
                        item.setCreditDate(parsedCreditDate);
                        item.setUpdatedOn(LocalDateTime.now());
                        logger.info("Updated credit item with id: {}", item.getId());
                    });
            } else {
                // Create new credit item
                CreditItem item = new CreditItem();
                item.setId(java.util.UUID.randomUUID().toString()); // Generate a new UUID for the item
                item.setAmount(amount);
                item.setCategory(category);
                item.setSubCategory(subCategory);
                item.setComments(comments);
                item.setCreditDate(parsedCreditDate);
                item.setUpdatedOn(LocalDateTime.now());
                
                // Add to credit record
                if (credit.getMonthlyCredits() == null) {
                    credit.setMonthlyCredits(new ArrayList<>());
                }
                credit.getMonthlyCredits().add(item);
                logger.info("Added new credit item with id: {}", item.getId());
            }
            
            credit.setModifiedAt(LocalDateTime.now());
            
            // Save updated credit record
            creditRepository.save(credit);
        } else {
            logger.warn("Credit record not found with id: {}", creditId);
        }
        
        return "redirect:/credits/" + creditId;
    }
    
    @GetMapping("/delete-item")
    public String deleteCreditItem(@RequestParam String creditId, @RequestParam String itemId) {
        logger.info("Deleting credit item {} from record {}", itemId, creditId);
        
        Optional<CreditRecord> creditOpt = creditRepository.findById(creditId);
        
        if (creditOpt.isPresent()) {
            CreditRecord credit = creditOpt.get();
            
            // Remove the item with the matching ID
            credit.getMonthlyCredits().removeIf(item -> item.getId().equals(itemId));
            credit.setModifiedAt(LocalDateTime.now());
            
            // Save updated credit record
            creditRepository.save(credit);
            logger.info("Deleted credit item with id: {}", itemId);
        } else {
            logger.warn("Credit record not found with id: {}", creditId);
        }
        
        return "redirect:/credits/" + creditId;
    }
}