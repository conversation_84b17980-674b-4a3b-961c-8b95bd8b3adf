package prs.lrh.maintenance.repository;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;
import prs.lrh.maintenance.model.CreditRecord;

import java.util.List;
import java.util.Optional;

@Repository
public interface CreditRepository extends MongoRepository<CreditRecord, String> {
    Optional<CreditRecord> findByMonthAndYear(int month, int year);
    List<CreditRecord> findByYear(int year);
    List<CreditRecord> findByOrderByYearDescMonthDesc();
}