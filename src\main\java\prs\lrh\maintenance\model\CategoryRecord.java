package prs.lrh.maintenance.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Document(collection = "category_records")
public class CategoryRecord {
    @Id
    private String id;
    private String category;
    private String type;
    private List<String> subCategory;
    private LocalDateTime createdAt;
    private LocalDateTime modifiedAt;
}