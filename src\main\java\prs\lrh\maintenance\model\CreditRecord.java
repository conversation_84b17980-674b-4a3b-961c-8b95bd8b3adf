package prs.lrh.maintenance.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Document(collection = "credit_records")
public class CreditRecord {
    @Id
    private String id;
    private int month;
    private int year;
    private List<CreditItem> monthlyCredits;
    private LocalDateTime createdAt;
    private LocalDateTime modifiedAt;
}