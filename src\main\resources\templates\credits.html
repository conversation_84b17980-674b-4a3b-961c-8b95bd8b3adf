<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:replace="~{fragments/layout :: head('Credit Records')}">
</head>
<body>
    <div th:replace="~{fragments/layout :: header}"></div>
    
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Credit Records</h1>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">Search Credit Records</div>
            <div class="card-body">
                <form th:action="@{/credits}" method="get" class="row g-3">
                    <div class="col-md-5">
                        <label for="month" class="form-label">Month</label>
                        <select class="form-select" id="month" name="month">
                            <option value="">Select Month</option>
                            <option value="1" th:selected="${param.month != null && param.month[0] == '1'}">January</option>
                            <option value="2" th:selected="${param.month != null && param.month[0] == '2'}">February</option>
                            <option value="3" th:selected="${param.month != null && param.month[0] == '3'}">March</option>
                            <option value="4" th:selected="${param.month != null && param.month[0] == '4'}">April</option>
                            <option value="5" th:selected="${param.month != null && param.month[0] == '5'}">May</option>
                            <option value="6" th:selected="${param.month != null && param.month[0] == '6'}">June</option>
                            <option value="7" th:selected="${param.month != null && param.month[0] == '7'}">July</option>
                            <option value="8" th:selected="${param.month != null && param.month[0] == '8'}">August</option>
                            <option value="9" th:selected="${param.month != null && param.month[0] == '9'}">September</option>
                            <option value="10" th:selected="${param.month != null && param.month[0] == '10'}">October</option>
                            <option value="11" th:selected="${param.month != null && param.month[0] == '11'}">November</option>
                            <option value="12" th:selected="${param.month != null && param.month[0] == '12'}">December</option>
                        </select>
                    </div>
                    <div class="col-md-5">
                        <label for="year" class="form-label">Year</label>
                        <input type="number" class="form-control" id="year" name="year" 
                               th:value="${param.year != null ? param.year[0] : currentYear}" 
                               min="2020" max="2030">
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">Search</button>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="mb-3">
            <a th:href="@{/credits}" class="btn btn-outline-secondary">Clear Search</a>
        </div>
        
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>Month</th>
                    <th>Year</th>
                    <th>Number of Credits</th>
                    <th>Total Amount</th>
                    <th>Last Modified</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <tr th:each="credit : ${credits}">
                    <td th:text="${credit.month == 1 ? 'January' : 
                                  credit.month == 2 ? 'February' : 
                                  credit.month == 3 ? 'March' : 
                                  credit.month == 4 ? 'April' : 
                                  credit.month == 5 ? 'May' : 
                                  credit.month == 6 ? 'June' : 
                                  credit.month == 7 ? 'July' : 
                                  credit.month == 8 ? 'August' : 
                                  credit.month == 9 ? 'September' : 
                                  credit.month == 10 ? 'October' : 
                                  credit.month == 11 ? 'November' : 'December'}"></td>
                    <td th:text="${credit.year}"></td>
                    <td th:text="${credit.monthlyCredits != null ? credit.monthlyCredits.size() : 0}"></td>
                    <td th:text="${credit.monthlyCredits != null ? #aggregates.sum(credit.monthlyCredits.![amount]) : 0}"></td>
                    <td th:text="${#temporals.format(credit.modifiedAt, 'dd-MM-yyyy HH:mm')}"></td>
                    <td>
                        <a th:href="@{/credits/{id}(id=${credit.id})}" class="btn btn-sm btn-info">View Details</a>
                    </td>
                </tr>
                <tr th:if="${#lists.isEmpty(credits)}">
                    <td colspan="6" class="text-center">No credit records found</td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div th:replace="~{fragments/layout :: footer}"></div>
</body>
</html>