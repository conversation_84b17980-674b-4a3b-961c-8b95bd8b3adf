FROM eclipse-temurin:21-jdk-alpine as build
WORKDIR /workspace/app

COPY gradle gradle
COPY gradlew .
COPY gradlew.bat .
# Make gradlew executable
RUN chmod +x ./gradlew

COPY build.gradle .
COPY settings.gradle .
COPY src src

RUN ./gradlew build -x test
# Check what files were built
RUN ls -la build/libs/

FROM eclipse-temurin:21-jre-alpine
VOLUME /tmp
WORKDIR /app

# Copy the JAR file
COPY --from=build /workspace/app/build/libs/*.jar app.jar

# Add PORT environment variable for Render.com
ENV PORT=8080
EXPOSE ${PORT}

ENTRYPOINT ["java", "-jar", "/app/app.jar"]


