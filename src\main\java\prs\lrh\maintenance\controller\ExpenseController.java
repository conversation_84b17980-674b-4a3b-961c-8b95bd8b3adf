package prs.lrh.maintenance.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import com.lowagie.text.Document;
import com.lowagie.text.DocumentException;
import com.lowagie.text.Element;
import com.lowagie.text.FontFactory;
import com.lowagie.text.Image;
import com.lowagie.text.Paragraph;
import com.lowagie.text.Phrase;
import com.lowagie.text.Font;
import com.lowagie.text.pdf.PdfContentByte;
import com.lowagie.text.pdf.PdfPCell;
import com.lowagie.text.pdf.PdfPTable;
import com.lowagie.text.pdf.PdfPageEventHelper;
import com.lowagie.text.pdf.PdfWriter;

import prs.lrh.maintenance.model.CategoryRecord;
import prs.lrh.maintenance.model.ExpenseItem;
import prs.lrh.maintenance.model.ExpenseRecord;
import prs.lrh.maintenance.repository.CategoryRepository;
import prs.lrh.maintenance.repository.ExpenseRepository;

import java.awt.Color;
import java.io.ByteArrayOutputStream;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Map;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/expenses")
public class ExpenseController {
    
    private static final Logger logger = LoggerFactory.getLogger(ExpenseController.class);
    
    @Autowired
    private ExpenseRepository expenseRepository;
    
    @Autowired
    private CategoryRepository categoryRepository;
    
    @GetMapping
    public String listExpenses(Model model, 
                              @RequestParam(required = false) Integer month,
                              @RequestParam(required = false) Integer year) {
        
        List<ExpenseRecord> expenses;
        
        // If both month and year are provided
        if (month != null && year != null) {
            Optional<ExpenseRecord> expenseOpt = expenseRepository.findByMonthAndYear(month, year);
            if (expenseOpt.isPresent()) {
                expenses = List.of(expenseOpt.get());
            } else {
                // Check if this is current or future month
                YearMonth requestedYearMonth = YearMonth.of(year, month);
                YearMonth currentYearMonth = YearMonth.now();
                
                if (requestedYearMonth.equals(currentYearMonth) || requestedYearMonth.isAfter(currentYearMonth)) {
                    // Create a new empty expense record for the month
                    ExpenseRecord newExpense = createNewExpenseRecord(month, year);
                    expenses = List.of(newExpense);
                } else {
                    expenses = new ArrayList<>();
                }
            }
        } 
        // If only year is provided
        else if (year != null) {
            expenses = expenseRepository.findByYear(year);
        } 
        // If no filters are provided
        else {
            // Get current month and year
            YearMonth currentYearMonth = YearMonth.now();
            int currentMonth = currentYearMonth.getMonthValue();
            int currentYear = currentYearMonth.getYear();
            
            // Try to get records for current month
            Optional<ExpenseRecord> currentMonthExpense = expenseRepository.findByMonthAndYear(currentMonth, currentYear);
            
            if (currentMonthExpense.isPresent()) {
                expenses = List.of(currentMonthExpense.get());
            } else {
                // Try previous month
                YearMonth previousYearMonth = currentYearMonth.minusMonths(1);
                Optional<ExpenseRecord> previousMonthExpense = expenseRepository.findByMonthAndYear(
                    previousYearMonth.getMonthValue(), 
                    previousYearMonth.getYear());
                
                if (previousMonthExpense.isPresent()) {
                    expenses = List.of(previousMonthExpense.get());
                } else {
                    // Get all records ordered by year and month
                    expenses = expenseRepository.findByOrderByYearDescMonthDesc();
                }
            }
        }
        
        logger.info("Found {} expense records", expenses.size());
        model.addAttribute("expenses", expenses);
        model.addAttribute("currentMonth", YearMonth.now().getMonthValue());
        model.addAttribute("currentYear", YearMonth.now().getYear());
        return "expenses";
    }
    
    @GetMapping("/{id}")
    public String viewExpense(@PathVariable String id, Model model) {
        logger.debug("Viewing expense record with id: {}", id);
        Optional<ExpenseRecord> expenseRecord = expenseRepository.findById(id);
        
        if (expenseRecord.isPresent()) {
            ExpenseRecord expense = expenseRecord.get();
            logger.debug("Expense record found: {}", expense.getId());
            
            // Get expense categories - only get EXPENSE_TYPE categories
            List<CategoryRecord> expenseCategories = categoryRepository.findByTypeOrderByCategoryAsc("EXPENSE_TYPE");
            
            model.addAttribute("expense", expense);
            model.addAttribute("expenseCategories", expenseCategories);
            
            // Calculate total expense amount
            double totalAmount = expense.getMonthlyExpenses().stream()
                .mapToDouble(ExpenseItem::getAmount)
                .sum();
            
            model.addAttribute("totalAmount", totalAmount);
            
            return "expense-detail";
        } else {
            logger.warn("Expense record not found with id: {}", id);
            return "redirect:/expenses";
        }
    }
    
    private ExpenseRecord createNewExpenseRecord(int month, int year) {
        logger.debug("Creating new expense record for {}/{}", month, year);
        
        // Create a new expense record
        ExpenseRecord expense = new ExpenseRecord();
        expense.setMonth(month);
        expense.setYear(year);
        expense.setMonthlyExpenses(new ArrayList<>());
        expense.setCreatedAt(LocalDateTime.now());
        expense.setModifiedAt(LocalDateTime.now());
        
        // Save the new expense record
        ExpenseRecord saved = expenseRepository.save(expense);
        logger.info("Created new expense record with id: {}", saved.getId());
        return saved;
    }
    
    @PostMapping("/add-item")
    public String addExpenseItem(@RequestParam String expenseId,
                                @RequestParam double amount,
                                @RequestParam String category,
                                @RequestParam String subCategory,
                                @RequestParam String comments,
                                @RequestParam String expenseDate,
                                @RequestParam(required = false) String itemId,
                                @RequestParam(required = false, defaultValue = "false") boolean editMode) {
        
        logger.info("Adding/updating expense item to record: {}", expenseId);
        
        Optional<ExpenseRecord> expenseOpt = expenseRepository.findById(expenseId);
        
        if (expenseOpt.isPresent()) {
            ExpenseRecord expense = expenseOpt.get();
            
            // Parse the expense date
            LocalDateTime parsedExpenseDate;
            try {
                parsedExpenseDate = LocalDateTime.parse(expenseDate);
            } catch (Exception e) {
                logger.error("Error parsing expense date: {}", e.getMessage());
                parsedExpenseDate = LocalDateTime.now();
            }
            
            if (editMode && itemId != null && !itemId.isEmpty()) {
                // Update existing expense item
                boolean itemFound = false;
                for (ExpenseItem item : expense.getMonthlyExpenses()) {
                    if (item.getId() != null && item.getId().equals(itemId)) {
                        item.setAmount(amount);
                        item.setCategory(category);
                        item.setSubCategory(subCategory);
                        item.setComments(comments);
                        item.setExpenseDate(parsedExpenseDate);
                        item.setUpdatedOn(LocalDateTime.now());
                        itemFound = true;
                        logger.info("Updated expense item with id: {}", itemId);
                        break;
                    }
                }
                
                if (!itemFound) {
                    logger.warn("Expense item not found with id: {}", itemId);
                }
            } else {
                // Create new expense item
                ExpenseItem item = new ExpenseItem();
                item.setId(java.util.UUID.randomUUID().toString()); // Generate a new UUID for the item
                item.setAmount(amount);
                item.setCategory(category);
                item.setSubCategory(subCategory);
                item.setComments(comments);
                item.setExpenseDate(parsedExpenseDate);
                item.setUpdatedOn(LocalDateTime.now());
                
                // Add to expense record
                if (expense.getMonthlyExpenses() == null) {
                    expense.setMonthlyExpenses(new ArrayList<>());
                }
                expense.getMonthlyExpenses().add(item);
                logger.info("Added new expense item with id: {}", item.getId());
            }
            
            expense.setModifiedAt(LocalDateTime.now());
            
            // Save updated expense record
            expenseRepository.save(expense);
        } else {
            logger.warn("Expense record not found with id: {}", expenseId);
        }
        
        return "redirect:/expenses/" + expenseId;
    }
    
    @GetMapping("/delete-item/{expenseId}/{index}")
    public String deleteExpenseItem(@PathVariable String expenseId, @PathVariable int index) {
        logger.info("Deleting expense item at index {} from record: {}", index, expenseId);
        
        Optional<ExpenseRecord> expenseOpt = expenseRepository.findById(expenseId);
        
        if (expenseOpt.isPresent()) {
            ExpenseRecord expense = expenseOpt.get();
            
            if (expense.getMonthlyExpenses() != null && index >= 0 && index < expense.getMonthlyExpenses().size()) {
                expense.getMonthlyExpenses().remove(index);
                expense.setModifiedAt(LocalDateTime.now());
                
                // Save updated expense record
                expenseRepository.save(expense);
                logger.info("Successfully deleted expense item");
            } else {
                logger.warn("Invalid index: {} for expense record: {}", index, expenseId);
            }
        } else {
            logger.warn("Expense record not found with id: {}", expenseId);
        }
        
        return "redirect:/expenses/" + expenseId;
    }
    
    @GetMapping("/pdf/{id}")
    @ResponseBody
    public ResponseEntity<byte[]> generateExpensePdf(@PathVariable String id) throws DocumentException {
        // Get the expense record
        Optional<ExpenseRecord> expenseOpt = expenseRepository.findById(id);
        
        if (!expenseOpt.isPresent()) {
            return ResponseEntity.notFound().build();
        }
        
        ExpenseRecord expense = expenseOpt.get();
        
        // Calculate total amount
        double totalAmount = expense.getMonthlyExpenses().stream()
                .mapToDouble(item -> item.getAmount())
                .sum();
        
        // Create PDF document
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        Document document = new Document();
        PdfWriter writer = PdfWriter.getInstance(document, baos);
        
        // Add page event handler for the logo (if needed)
        writer.setPageEvent(new PdfPageEventHelper() {
            public void onEndPage(PdfWriter writer, Document document) {
                try {
                    // Load the logo image if needed
                    Image logo = Image.getInstance(new ClassPathResource("static/images/lake_ridge_homes_header.jpg").getURL());
                    
                    // Scale the logo
                    float pageWidth = document.getPageSize().getWidth();
                    float logoWidth = pageWidth * 0.5f;
                    logo.scaleToFit(logoWidth, 60);
                    
                    // Position the logo at the right side of the header
                    logo.setAbsolutePosition(pageWidth - logoWidth - 20, document.getPageSize().getHeight() - 70);
                    
                    // Add the logo to the page
                    PdfContentByte cb = writer.getDirectContent();
                    cb.addImage(logo);
                } catch (Exception e) {
                    logger.error("Error adding logo to PDF: {}", e.getMessage());
                }
            }
        });
        
        // Adjust margins
        document.setMargins(document.leftMargin(), document.rightMargin(), 10, document.bottomMargin());
        
        document.open();
        
        // Add title aligned to the left
        com.lowagie.text.Font titleFont = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 18);
        Paragraph title = new Paragraph("Expenditure Report", titleFont);
        title.setAlignment(Element.ALIGN_LEFT);
        document.add(title);
        
        // Add month and year aligned to the left
        String monthName = getMonthName(expense.getMonth());
        Font subtitleFont = FontFactory.getFont(FontFactory.HELVETICA, 14);
        Paragraph subtitle = new Paragraph(monthName + " " + expense.getYear(), subtitleFont);
        subtitle.setAlignment(Element.ALIGN_LEFT);
        subtitle.setSpacingBefore(5);
        subtitle.setSpacingAfter(20);
        document.add(subtitle);
        
        // Add summary
        Font summaryFont = FontFactory.getFont(FontFactory.HELVETICA, 12);
        Paragraph summary = new Paragraph();
        summary.setFont(summaryFont);
        summary.add("Total Expenses: " + expense.getMonthlyExpenses().size() + "\n");
        summary.add("Total Amount: Rs." + String.format("%,.2f", totalAmount) + "\n");
        summary.setSpacingAfter(20);
        document.add(summary);
        
        // Group expenses by category
        Map<String, List<ExpenseItem>> expensesByCategory = expense.getMonthlyExpenses().stream()
                .collect(Collectors.groupingBy(ExpenseItem::getCategory));
        
        // Define table headers
        String[] headers = {"Expense Type", "Amount", "Expense Date", "Updated On", "Comments"};
        Font headerFont = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 10);
        Font rowFont = FontFactory.getFont(FontFactory.HELVETICA, 9);
        Font totalFont = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 9);
            
        // Create expense items table for this category
        PdfPTable itemsTable = new PdfPTable(5);
        itemsTable.setWidthPercentage(100);
        float[] columnWidths = {2.0f, 1.5f, 1.5f, 1.5f, 3.5f};
        itemsTable.setWidths(columnWidths);
        
        // Add table headers
        for (String headerTitle : headers) {
            PdfPCell header = new PdfPCell();
            header.setBackgroundColor(new Color(66, 139, 202));
            header.setHorizontalAlignment(Element.ALIGN_CENTER);
            header.setVerticalAlignment(Element.ALIGN_MIDDLE);
            header.setPadding(5);
            header.setPhrase(new Phrase(headerTitle, headerFont));
            itemsTable.addCell(header);
        }
        
        // Process each category
        for (Map.Entry<String, List<ExpenseItem>> entry : expensesByCategory.entrySet()) {
            String category = entry.getKey();
            List<ExpenseItem> items = entry.getValue();
            
            // Calculate category total
            double categoryTotal = items.stream()
                    .mapToDouble(ExpenseItem::getAmount)
                    .sum();
            
            // Add table rows for this category
            for (ExpenseItem item : items) {
                // Expense Type (Sub-Category)
                PdfPCell cell = new PdfPCell(new Phrase(item.getSubCategory(), rowFont));
                cell.setPadding(5);
                itemsTable.addCell(cell);
                
                // Amount
                cell = new PdfPCell(new Phrase("Rs. " + String.format("%,.2f", item.getAmount()), rowFont));
                cell.setPadding(5);
                cell.setHorizontalAlignment(Element.ALIGN_RIGHT);
                itemsTable.addCell(cell);
                
                // Expense Date (only date part)
                cell = new PdfPCell(new Phrase(
                    item.getExpenseDate().format(DateTimeFormatter.ofPattern("dd-MM-yyyy")), rowFont));
                cell.setPadding(5);
                itemsTable.addCell(cell);
                
                // Updated On
                cell = new PdfPCell(new Phrase(
                    item.getUpdatedOn() != null ? 
                    item.getUpdatedOn().format(DateTimeFormatter.ofPattern("dd-MM-yyyy")) : "", 
                    rowFont));
                cell.setPadding(5);
                itemsTable.addCell(cell);
                
                // Comments
                cell = new PdfPCell(new Phrase(item.getComments() != null ? item.getComments() : "", rowFont));
                cell.setPadding(5);
                itemsTable.addCell(cell);
            }
            
            // Add category total
            PdfPCell totalCell = new PdfPCell(new Phrase(
                "Total Expenses (" + category + "): Rs." + String.format("%,.2f", categoryTotal), 
                totalFont));//₹
            totalCell.setColspan(5);
            totalCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
            totalCell.setPadding(5);
            totalCell.setBackgroundColor(new Color(230, 230, 250)); // Light lavender color
            itemsTable.addCell(totalCell);
        }
            
        document.add(itemsTable);
        
        document.close();
        
        // Prepare response
        byte[] pdfBytes = baos.toByteArray();
        
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_PDF);
        httpHeaders.setContentDispositionFormData("attachment", 
                "Expense_Report_"+ LocalDateTime.now().format(DateTimeFormatter.ofPattern("dd-MM-yyyy_HH-mm")) +".pdf");
                httpHeaders.setCacheControl("must-revalidate, post-check=0, pre-check=0");
        
        return ResponseEntity
                .ok()
                .headers(httpHeaders)
                .contentLength(pdfBytes.length)
                .body(pdfBytes);
    }
    
    private String getMonthName(int month) {
        return month == 1 ? "January" : 
               month == 2 ? "February" : 
               month == 3 ? "March" : 
               month == 4 ? "April" : 
               month == 5 ? "May" : 
               month == 6 ? "June" : 
               month == 7 ? "July" : 
               month == 8 ? "August" : 
               month == 9 ? "September" : 
               month == 10 ? "October" : 
               month == 11 ? "November" : "December";
    }
}





