<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:replace="~{fragments/layout :: head('Categories')}">
</head>
<body>
    <div th:replace="~{fragments/layout :: header}"></div>
    
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Categories</h1>
            <a th:href="@{/categories/new}" class="btn btn-primary">Add New Category</a>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">Filter Categories</div>
            <div class="card-body">
                <form th:action="@{/categories}" method="get" class="row g-3">
                    <div class="col-md-8">
                        <label for="type" class="form-label">Type</label>
                        <select class="form-select" id="type" name="type">
                            <option value="">All Types</option>
                            <option th:each="type : ${types}" th:value="${type}" th:text="${type}"></option>
                        </select>
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">Filter</button>
                        <a th:href="@{/categories}" class="btn btn-outline-secondary">Clear</a>
                    </div>
                </form>
            </div>
        </div>
        
        <div th:if="${param.error}" class="alert alert-danger" role="alert">
            <span th:text="${param.error}"></span>
        </div>
        
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>Category</th>
                    <th>Type</th>
                    <th>Sub-Categories</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <tr th:each="category : ${categories}">
                    <td th:text="${category.category}"></td>
                    <td th:text="${category.type}"></td>
                    <td>
                        <span th:each="subCat, iterStat : ${category.subCategory}">
                            <span th:text="${subCat}"></span>
                            <span th:if="${!iterStat.last}">, </span>
                        </span>
                    </td>
                    <td>
                        <a th:href="@{/categories/{id}(id=${category.id})}" class="btn btn-sm btn-info">Edit</a>
                        <a th:href="@{/categories/delete/{id}(id=${category.id})}" 
                           class="btn btn-sm btn-danger"
                           onclick="return confirm('Are you sure you want to delete this category?')">Delete</a>
                    </td>
                </tr>
                <tr th:if="${#lists.isEmpty(categories)}">
                    <td colspan="4" class="text-center">No categories found</td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div th:replace="~{fragments/layout :: footer}"></div>
</body>
</html>