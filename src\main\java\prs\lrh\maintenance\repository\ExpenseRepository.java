package prs.lrh.maintenance.repository;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;
import prs.lrh.maintenance.model.ExpenseRecord;

import java.util.List;
import java.util.Optional;

@Repository
public interface ExpenseRepository extends MongoRepository<ExpenseRecord, String> {
    Optional<ExpenseRecord> findByMonthAndYear(int month, int year);
    List<ExpenseRecord> findByYear(int year);
    List<ExpenseRecord> findByOrderByYearDescMonthDesc();
}