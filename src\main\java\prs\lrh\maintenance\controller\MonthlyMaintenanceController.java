package prs.lrh.maintenance.controller;

import com.mongodb.client.result.UpdateResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import prs.lrh.maintenance.model.HouseRecord;
import prs.lrh.maintenance.model.MonthlyMaintenance;
import prs.lrh.maintenance.model.MonthlyMaintenanceRecord;
import prs.lrh.maintenance.model.MonthlyReading;
import prs.lrh.maintenance.model.WaterReading;
import prs.lrh.maintenance.service.MongoDBService;
import prs.lrh.maintenance.repository.HouseRepository;
import prs.lrh.maintenance.repository.MonthlyMaintenanceRepository;
import prs.lrh.maintenance.repository.MonthlyReadingRepository;

import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/maintenance")
public class MonthlyMaintenanceController {

    private static final Logger logger = LoggerFactory.getLogger(MonthlyMaintenanceController.class);

    @Autowired
    private MonthlyMaintenanceRepository monthlyMaintenanceRepository;
    
    @Autowired
    private HouseRepository houseRepository;
    
    @Autowired
    private MonthlyReadingRepository monthlyReadingRepository;
    
    @Autowired
    private MongoDBService mongoDBService;

    @GetMapping
    public String listMaintenanceRecords(Model model, 
                                        @RequestParam(required = false) Integer month,
                                        @RequestParam(required = false) Integer year) {
        
        logger.debug("Listing maintenance records - month: {}, year: {}", month, year);
        
        List<MonthlyMaintenance> maintenanceRecords;
        boolean showCreateButton = false;
        
        // If both month and year are provided
        if (month != null && year != null) {
            maintenanceRecords = monthlyMaintenanceRepository.findByMonthAndYear(month, year);
            
            // Check if this is current month and no records exist
            YearMonth requestedYearMonth = YearMonth.of(year, month);
            YearMonth currentYearMonth = YearMonth.now();
            
            if (maintenanceRecords.isEmpty() && requestedYearMonth.equals(currentYearMonth)) {
                // Show create button for current month if no records exist
                showCreateButton = true;
            }
        } 
        // If only year is provided
        else if (year != null) {
            maintenanceRecords = monthlyMaintenanceRepository.findByYear(year);
        } 
        // If no filters are provided
        else {
            // Get current month and year
            YearMonth currentYearMonth = YearMonth.now();
            int currentMonth = currentYearMonth.getMonthValue();
            int currentYear = currentYearMonth.getYear();
            
            // Try to get records for current month
            maintenanceRecords = monthlyMaintenanceRepository.findByMonthAndYear(currentMonth, currentYear);
            
            // If no records found for current month, try previous month
            if (maintenanceRecords.isEmpty()) {
                YearMonth previousYearMonth = currentYearMonth.minusMonths(1);
                maintenanceRecords = monthlyMaintenanceRepository.findByMonthAndYear(
                    previousYearMonth.getMonthValue(), 
                    previousYearMonth.getYear());
            }
            
            // If still no records, get all records ordered by year and month
            if (maintenanceRecords.isEmpty()) {
                maintenanceRecords = monthlyMaintenanceRepository.findByOrderByYearDescMonthDesc();
            }
        }
        
        logger.info("Found {} maintenance records", maintenanceRecords.size());
        model.addAttribute("maintenanceRecords", maintenanceRecords);
        model.addAttribute("showCreateButton", showCreateButton);
        model.addAttribute("currentMonth", YearMonth.now().getMonthValue());
        model.addAttribute("currentYear", YearMonth.now().getYear());
        return "maintenance-records";
    }
    
    @GetMapping("/{id}")
    public String viewMaintenanceRecord(@PathVariable String id, Model model) {
        logger.debug("Viewing maintenance record with id: {}", id);
        Optional<MonthlyMaintenance> maintenanceRecord = monthlyMaintenanceRepository.findById(id);
        
        if (maintenanceRecord.isPresent()) {
            logger.debug("Maintenance record found: {}", maintenanceRecord.get().getId());
            
            // Sort the maintenance records by houseId
            MonthlyMaintenance maintenance = maintenanceRecord.get();
            List<MonthlyMaintenanceRecord> sortedRecords = maintenance.getMonthlyMaintenanceRecords()
                .stream()
                .sorted(Comparator.comparing(MonthlyMaintenanceRecord::getHouseId))
                .collect(Collectors.toList());
            
            maintenance.setMonthlyMaintenanceRecords(sortedRecords);
            model.addAttribute("maintenanceRecord", maintenance);
            
            return "maintenance-detail";
        } else {
            logger.warn("Maintenance record not found with id: {}", id);
            return "redirect:/maintenance";
        }
    }
    
    private MonthlyMaintenance createNewMonthlyMaintenance(int month, int year) {
        logger.debug("Creating new monthly maintenance record for {}/{}", month, year);
        
        // Create a new monthly maintenance
        MonthlyMaintenance maintenance = new MonthlyMaintenance();
        maintenance.setMonth(month);
        maintenance.setYear(year);
        
        // Get all houses sorted by ID
        List<HouseRecord> houses = houseRepository.findAllByOrderByIdAsc();

        List<MonthlyReading> readings = monthlyReadingRepository.findByMonthAndYear(month, year);
        Map<Long, WaterReading> readingsMap = readings.stream()
            .flatMap(reading -> reading.getWaterReadings().stream())
            .collect(Collectors.toMap(WaterReading::getHouseId, waterReading -> waterReading, 
                                     (existing, replacement) -> existing));
        
        // Create maintenance records for each house
        List<MonthlyMaintenanceRecord> maintenanceRecords = houses.stream()
            .map(house -> {
                MonthlyMaintenanceRecord record = new MonthlyMaintenanceRecord();
                record.setHouseId(house.getId());
                record.setHouseNumber(house.getHouseNumber());
                record.setFixedAmt(house.getRate()); // Default values
                record.setWaterBill(readingsMap.containsKey(house.getId()) ? 
                    calculateWaterBillFromReading(readingsMap.get(house.getId())) : 0.00);
                record.setGarbage(house.getGarbage());
                record.setOtherCharges(0.00);
                record.setPastDues(house.getBalance());
                double total = record.getFixedAmt() + record.getWaterBill() + record.getGarbage() + record.getOtherCharges() + record.getPastDues();
                record.setTotal(total);
                record.setCreatedAt(LocalDateTime.now());
                record.setModifiedAt(LocalDateTime.now());
                logger.debug("Created maintenance record for house: {}", house.getHouseNumber());
                house.setBalance(total);
                mongoDBService.updateByFilterMap("house_records", Map.of("_id", house.getId()), 
                    Map.of("balance", total, "modifiedAt", LocalDateTime.now()));
                return record;
            })
            .collect(Collectors.toList());
        
        maintenance.setMonthlyMaintenanceRecords(maintenanceRecords);
        
        // Save the new maintenance record
        MonthlyMaintenance saved = monthlyMaintenanceRepository.save(maintenance);
        logger.info("Created new monthly maintenance record with id: {}", saved.getId());
        return saved;
    }
    
    @PostMapping("/update")
    public String updateMaintenanceRecord(@RequestParam String maintenanceId,
                                         @RequestParam int month,
                                         @RequestParam int year,
                                         @RequestParam("records") List<MonthlyMaintenanceRecord> updatedRecords) {
        
        logger.info("Updating maintenance record - id: {}, month: {}/{}", maintenanceId, month, year);
        
        Optional<MonthlyMaintenance> optionalMaintenance = monthlyMaintenanceRepository.findById(maintenanceId);
        
        if (optionalMaintenance.isPresent()) {
            MonthlyMaintenance maintenance = optionalMaintenance.get();
            
            // Sort the updated records by houseId
            List<MonthlyMaintenanceRecord> sortedRecords = updatedRecords.stream()
                .sorted(Comparator.comparing(MonthlyMaintenanceRecord::getHouseId))
                .collect(Collectors.toList());
            
            // Update the records
            maintenance.setMonthlyMaintenanceRecords(sortedRecords);
            
            // Update modified timestamp for each record
            for (MonthlyMaintenanceRecord record : maintenance.getMonthlyMaintenanceRecords()) {
                record.setModifiedAt(LocalDateTime.now());
            }
            
            // Save updated maintenance
            monthlyMaintenanceRepository.save(maintenance);
            logger.info("Maintenance record updated successfully");
            
            return "redirect:/maintenance/" + maintenanceId;
        }
        
        logger.warn("Maintenance record not found with id: {}", maintenanceId);
        return "redirect:/maintenance";
    }

    @GetMapping("/create")
    public String createMaintenanceRecords(@RequestParam int month, @RequestParam int year) {
        logger.info("Creating maintenance records for {}/{}", month, year);
        
        // Check if records already exist for this month/year
        List<MonthlyMaintenance> existingRecords = monthlyMaintenanceRepository.findByMonthAndYear(month, year);
        if (!existingRecords.isEmpty()) {
            logger.warn("Maintenance records already exist for {}/{}", month, year);
            return "redirect:/maintenance?month=" + month + "&year=" + year;
        }
        
        // Create new records
        MonthlyMaintenance newRecord = createNewMonthlyMaintenance(month, year);
        
        return "redirect:/maintenance/" + newRecord.getId();
    }

    private double calculateWaterBillFromReading(WaterReading waterReading) {
        if (waterReading == null) {
            return 0.00;
        }
        
        // Find the water reading for this house
        int units = waterReading.getUnits();
        // Tiered pricing: 0-30 units at 20.00, units > 30 at 30.00
        if (units <= 30) {
            return units * 20.00;
        } else {
            return (30 * 20.00) + ((units - 30) * 30.00);
        }
    }

    @PostMapping("/update-record")
    public String updateSingleRecord(@RequestParam String maintenanceId,
                                    @RequestParam String recordId,
                                    @RequestParam int month,
                                    @RequestParam int year,
                                    @RequestParam Long houseId,
                                    @RequestParam String houseNumber,
                                    @RequestParam double fixedAmt,
                                    @RequestParam double waterBill,
                                    @RequestParam double garbage,
                                    @RequestParam double otherCharges,
                                    @RequestParam double pastDues,
                                    @RequestParam double total,
                                    @RequestParam(required = false) String comments) {
        
        logger.info("Updating single maintenance record - Input parameters:");
        logger.info("maintenanceId: {}", maintenanceId);
        logger.info("recordId: {}", recordId);
        logger.info("month/year: {}/{}", month, year);
        logger.info("houseId: {}, houseNumber: {}", houseId, houseNumber);
        logger.info("fixedAmt: {}, waterBill: {}, garbage: {}, otherCharges: {}", fixedAmt, waterBill, garbage, otherCharges);
        logger.info("pastDues: {}, total: {}", pastDues, total);
        logger.info("comments: {}", comments);
        
        if (maintenanceId == null || houseId == null) {
            logger.error("Missing required parameters");
            return "redirect:/maintenance";
        }

        // Update using MongoDB service
        Map<String, Object> updates = Map.of("fixedAmt", fixedAmt, "waterBill", waterBill,
         "garbage", garbage, "otherCharges", otherCharges, "pastDues", pastDues, 
         "total", total,"comments", comments,"modifiedAt", LocalDateTime.now());
        
        UpdateResult result = mongoDBService.updateMaintenanceRecordFields(
            maintenanceId, 
            houseId, 
            updates
        );
        
        logger.info("Updated maintenance record: {}", result.getModifiedCount() > 0);
        
        // Update house balance
        if (result.getModifiedCount() > 0) {
            mongoDBService.updateByFilterMap("house_records", Map.of("_id", houseId), 
                Map.of("balance", total, "modifiedAt", LocalDateTime.now()));
            logger.info("Successfully updated house record balance");
        }
        
        return "redirect:/maintenance/" + maintenanceId;
    }
}


