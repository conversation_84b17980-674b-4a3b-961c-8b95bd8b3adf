package prs.lrh.maintenance.repository;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;
import prs.lrh.maintenance.model.AccountBalance;

import java.util.List;
import java.util.Optional;

@Repository
public interface AccountBalanceRepository extends MongoRepository<AccountBalance, String> {
    Optional<AccountBalance> findByMonthAndYear(int month, int year);
    List<AccountBalance> findByYear(int year);
    List<AccountBalance> findByYearOrderByMonthDesc(int year);
    List<AccountBalance> findByOrderByYearDescMonthDesc();
    List<AccountBalance> findByOrderByYearAscMonthAsc();
}
