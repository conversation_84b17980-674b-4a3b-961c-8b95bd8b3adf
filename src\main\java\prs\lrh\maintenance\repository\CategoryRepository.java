package prs.lrh.maintenance.repository;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;
import prs.lrh.maintenance.model.CategoryRecord;

import java.util.List;

@Repository
public interface CategoryRepository extends MongoRepository<CategoryRecord, String> {
    List<CategoryRecord> findByType(String type);
    CategoryRecord findByCategory(String category);
    List<CategoryRecord> findByTypeOrderByCategoryAsc(String type);
}