<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:replace="~{fragments/layout :: head('House Records')}">
</head>
<body>
    <div th:replace="~{fragments/layout :: header}"></div>
    
    <div class="container">
        <h1>House Records</h1>
        
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">Search by House Number</div>
                    <div class="card-body">
                        <form th:action="@{/houses}" method="get" class="row g-3">
                            <div class="col-md-8">
                                <input type="text" class="form-control" name="houseNumber" placeholder="Enter exact house number">
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-primary w-100">Search</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">Advanced Search</div>
                    <div class="card-body">
                        <form th:action="@{/houses}" method="get" class="row g-3">
                            <div class="col-md-4">
                                <select class="form-select" name="searchType">
                                    <option value="houseNumber">House Number</option>
                                    <option value="ownerName">Owner Name</option>
                                    <option value="residentName">Resident Name</option>
                                </select>
                            </div>
                            <div class="col-md-5">
                                <input type="text" class="form-control" name="searchTerm" placeholder="Enter search term">
                            </div>
                            <div class="col-md-3">
                                <button type="submit" class="btn btn-primary w-100">Search</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mb-3">
            <a th:href="@{/houses}" class="btn btn-outline-secondary">Clear Search</a>
            <button id="expandAllBtn" class="btn btn-outline-primary ms-2">Expand All</button>
            <button id="collapseAllBtn" class="btn btn-outline-primary ms-2">Collapse All</button>
        </div>
        
        <!-- Group houses in sets of 10 -->
        <div th:if="${not #lists.isEmpty(houses)}">
            <div th:each="group, groupStat : ${groupedHouses}">
                <div class="group-header" th:data-target="'group-' + ${groupStat.index}" 
                     onclick="toggleGroup(this.getAttribute('data-target'))">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>
                            <i class="bi bi-chevron-down toggle-icon"></i>
                            House Records Group #<span th:text="${groupStat.index + 1}"></span> 
                            (H.No's <span th:text="${group[0].houseNumber}"></span> - 
                            <span th:text="${group[group.size()-1].houseNumber}"></span>)
                        </span>
                        <span class="badge bg-primary rounded-pill" th:text="${group.size()}"></span>
                    </div>
                </div>
                
                <div th:id="'group-' + ${groupStat.index}" class="group-content" style="display: none;">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>House Number</th>
                                <th>Owner Name</th>
                                <th>Resident Name</th>
                                <th>Balance</th>
                                <th>Area</th>
                                <th>Rate</th>
                                <th>Garbage</th>
                                <th>Previous Water Reading</th>
                                <th>Current Water Reading</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr th:each="house : ${group}">
                                <td th:text="${house.id}"></td>
                                <td th:text="${house.houseNumber}"></td>
                                <td th:text="${house.ownerName}"></td>
                                <td th:text="${house.residentName}"></td>
                                <td th:text="${house.balance}"></td>
                                <td th:text="${house.area}"></td>
                                <td th:text="${house.rate}"></td>
                                <td th:text="${house.garbage}"></td>
                                <td th:text="${house.previousWaterMeterReading}"></td>
                                <td th:text="${house.currentWaterMeterReading}"></td>
                                <td>
                                    <a th:href="@{/payments(houseId=${house.id})}" class="btn btn-sm btn-info">View Payments</a>
                                    <a th:href="@{/payments/new(houseId=${house.id}, amtPayable=${house.balance})}" class="btn btn-sm btn-success">Pay</a>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <div th:if="${#lists.isEmpty(houses)}" class="alert alert-info">
            <p class="text-center">No house records found</p>
        </div>
    </div>
    
    <div th:replace="~{fragments/layout :: footer}"></div>
    
    <script>
        // Function to toggle group visibility
        function toggleGroup(groupId) {
            const groupContent = document.getElementById(groupId);
            const header = document.querySelector(`[data-target="${groupId}"]`);
            const icon = header.querySelector('.toggle-icon');
            
            if (groupContent.style.display === 'none') {
                groupContent.style.display = 'block';
                icon.classList.remove('rotate');
            } else {
                groupContent.style.display = 'none';
                icon.classList.add('rotate');
            }
        }
        
        // Expand all groups
        document.getElementById('expandAllBtn').addEventListener('click', function() {
            document.querySelectorAll('.group-content').forEach(function(group) {
                group.style.display = 'block';
            });
            document.querySelectorAll('.toggle-icon').forEach(function(icon) {
                icon.classList.remove('rotate');
            });
        });
        
        // Collapse all groups
        document.getElementById('collapseAllBtn').addEventListener('click', function() {
            document.querySelectorAll('.group-content').forEach(function(group) {
                group.style.display = 'none';
            });
            document.querySelectorAll('.toggle-icon').forEach(function(icon) {
                icon.classList.add('rotate');
            });
        });
        
        // Show the first group by default
        document.addEventListener('DOMContentLoaded', function() {
            const firstGroup = document.getElementById('group-0');
            if (firstGroup) {
                firstGroup.style.display = 'block';
                const firstHeader = document.querySelector('[data-target="group-0"]');
                if (firstHeader) {
                    const icon = firstHeader.querySelector('.toggle-icon');
                    if (icon) {
                        icon.classList.remove('rotate');
                    }
                }
            }
        });
    </script>
</body>
</html>

