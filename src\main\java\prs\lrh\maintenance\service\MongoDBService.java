package prs.lrh.maintenance.service;

import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.Updates;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import prs.lrh.maintenance.config.MongoDBClient;
import prs.lrh.maintenance.model.Payment;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Service class for custom MongoDB operations using the MongoDBClient.
 */
@Service
public class MongoDBService {

    private static final Logger logger = LoggerFactory.getLogger(MongoDBService.class);

    private final MongoDBClient mongoDBClient;

    
    public MongoDBService() {
        this.mongoDBClient = MongoDBClient.getInstance();
    }

    /**
     * Get a collection by name
     */
    public MongoCollection<Document> getCollection(String collectionName) {
        return mongoDBClient.getDatabase().getCollection(collectionName);
    }

    /**
     * Find a document by ID
     */
    public Document findById(String collectionName, String id) {
        MongoCollection<Document> collection = getCollection(collectionName);
        return collection.find(Filters.eq("_id", new ObjectId(id))).first();
    }

    /**
     * Find documents by a field value
     */
    public List<Document> findByField(String collectionName, String field, Object value) {
        MongoCollection<Document> collection = getCollection(collectionName);
        List<Document> results = new ArrayList<>();
        collection.find(Filters.eq(field, value)).into(results);
        return results;
    }

    /**
     * Find documents using a custom filter
     */
    public List<Document> findByFilter(String collectionName, Bson filter) {
        MongoCollection<Document> collection = getCollection(collectionName);
        List<Document> results = new ArrayList<>();
        collection.find(filter).into(results);
        return results;
    }

    /**
     * Insert a document
     */
    public void insertDocument(String collectionName, Document document) {
        MongoCollection<Document> collection = getCollection(collectionName);
        collection.insertOne(document);
    }

    /**
     * Update a document by ID
     */
    public UpdateResult updateDocument(String collectionName, String id, Document updates) {
        MongoCollection<Document> collection = getCollection(collectionName);
        
        // Convert the updates document to a list of update operations
        List<Bson> updateOperations = new ArrayList<>();
        for (String key : updates.keySet()) {
            updateOperations.add(Updates.set(key, updates.get(key)));
        }
        
        return collection.updateOne(
            Filters.eq("_id", new ObjectId(id)),
            Updates.combine(updateOperations)
        );
    }

    /**
     * Update documents matching a filter
     */
    public UpdateResult updateDocuments(String collectionName, Bson filter, Bson updates) {
        MongoCollection<Document> collection = getCollection(collectionName);
        return collection.updateMany(filter, updates);
    }

    /**
     * Update a field value in a document matching a filter
     * @param collectionName The collection name
     * @param filter The filter to find the document
     * @param fieldName The name of the field to update
     * @param value The new value for the field
     * @return The update result
     */
    public UpdateResult updateValueByField(String collectionName, Bson filter, String fieldName, Object value) {
        MongoCollection<Document> collection = getCollection(collectionName);
        
        // Create the update operation to set the field value
        Bson update = Updates.set(fieldName, value);
        
        // Execute the update
        return collection.updateOne(filter, update);
    }

    /**
     * Update a field value in a document by its ID
     * @param collectionName The collection name
     * @param id The document ID
     * @param fieldName The name of the field to update
     * @param value The new value for the field
     * @return The update result
     */
    public UpdateResult updateValueById(String collectionName, String id, String fieldName, Object value) {
        Bson filter = Filters.eq("_id", new ObjectId(id));
        return updateValueByField(collectionName, filter, fieldName, value);
    }
    
    /**
     * Update a field value in a document by a specific field value
     * @param collectionName The collection name
     * @param filterField The field name to filter by
     * @param filterValue The value to filter by
     * @param updateField The field name to update
     * @param updateValue The new value for the field
     * @return The update result
     */
    public UpdateResult updateValueByFieldValue(String collectionName, String filterField, Object filterValue, 
                                               String updateField, Object updateValue) {
        Bson filter = Filters.eq(filterField, filterValue);
        return updateValueByField(collectionName, filter, updateField, updateValue);
    }
    
    /**
     * Update multiple field values in a document matching a filter
     * @param collectionName The collection name
     * @param filter The filter to find the document
     * @param updates A map of field names to their new values
     * @return The update result
     */
    public UpdateResult updateMultipleValues(String collectionName, Bson filter, Map<String, Object> updates) {
        MongoCollection<Document> collection = getCollection(collectionName);
        
        // Create a list of update operations
        List<Bson> updateOperations = new ArrayList<>();
        for (Map.Entry<String, Object> entry : updates.entrySet()) {
            updateOperations.add(Updates.set(entry.getKey(), entry.getValue()));
        }
        
        // Combine all update operations
        Bson combinedUpdate = Updates.combine(updateOperations);
        
        // Execute the update
        return collection.updateOne(filter, combinedUpdate);
    }

    /**
     * Delete a document by ID
     */
    public DeleteResult deleteDocument(String collectionName, String id) {
        MongoCollection<Document> collection = getCollection(collectionName);
        return collection.deleteOne(Filters.eq("_id", new ObjectId(id)));
    }

    /**
     * Delete documents matching a filter
     */
    public DeleteResult deleteDocuments(String collectionName, Bson filter) {
        MongoCollection<Document> collection = getCollection(collectionName);
        return collection.deleteMany(filter);
    }

    /**
     * Count documents in a collection
     */
    public long countDocuments(String collectionName) {
        MongoCollection<Document> collection = getCollection(collectionName);
        return collection.countDocuments();
    }

    /**
     * Count documents matching a filter
     */
    public long countDocuments(String collectionName, Bson filter) {
        MongoCollection<Document> collection = getCollection(collectionName);
        return collection.countDocuments(filter);
    }

    /**
     * Check if a document exists
     */
    public boolean documentExists(String collectionName, Bson filter) {
        return countDocuments(collectionName, filter) > 0;
    }

    /**
     * Perform an aggregation pipeline
     */
    public List<Document> aggregate(String collectionName, List<Bson> pipeline) {
        MongoCollection<Document> collection = getCollection(collectionName);
        List<Document> results = new ArrayList<>();
        collection.aggregate(pipeline).into(results);
        return results;
    }

    /**
     * Add a payment to a payment record using MongoDB's $push operator
     * @param houseId The house ID to find the payment record
     * @param payment The payment to add
     * @return True if the payment was added successfully, false otherwise
     */
    public boolean addPaymentToRecord(Long houseId, Payment payment) {
        MongoCollection<Document> collection = getCollection("payment_records");
        
        // Convert Payment object to Document
        Document paymentDoc = new Document();
        paymentDoc.append("amtPayable", payment.getAmtPayable());
        paymentDoc.append("amtPaid", payment.getAmtPaid());
        paymentDoc.append("comments", payment.getComments());
        paymentDoc.append("month", payment.getMonth());
        paymentDoc.append("year", payment.getYear());
        paymentDoc.append("createdAt", payment.getCreatedAt());
        paymentDoc.append("modifiedAt", payment.getModifiedAt());
        
        // Create the filter to find the payment record
        Bson filter = Filters.eq("houseId", houseId);
        
        // Create the update operation to push the payment to the payments array
        Bson update = Updates.push("payments", paymentDoc);
        
        // Execute the update
        UpdateResult result = collection.updateOne(filter, update);
        
        // Return true if a document was modified
        return result.getModifiedCount() > 0;
    }

    /**
     * Find documents using a filter map
     * @param collectionName The collection name
     * @param filterMap Map of field names to their filter values
     * @return List of documents matching the filter
     */
    public List<Document> findByFilterMap(String collectionName, Map<String, Object> filterMap) {
        Bson filter = getFilter(filterMap);
        return findByFilter(collectionName, filter);
    }
    
    /**
     * Update documents using a filter map
     * @param collectionName The collection name
     * @param filterMap Map of field names to their filter values
     * @param updates The updates to apply
     * @return The update result
     */
    public UpdateResult updateByFilterMap(String collectionName, Map<String, Object> filterMap, Map<String, Object> updatesMap) {
        Bson filter = getFilter(filterMap);
        // Create update operations from the map
        List<Bson> updateOperations = new ArrayList<>();
        for (Map.Entry<String, Object> entry : updatesMap.entrySet()) {
            updateOperations.add(Updates.set(entry.getKey(), entry.getValue()));
        }
        
        // Combine all update operations
        Bson updates = Updates.combine(updateOperations);
        return updateDocuments(collectionName, filter, updates);
    }
    
    /**
     * Delete documents using a filter map
     * @param collectionName The collection name
     * @param filterMap Map of field names to their filter values
     * @return The delete result
     */
    public DeleteResult deleteByFilterMap(String collectionName, Map<String, Object> filterMap) {
        Bson filter = getFilter(filterMap);
        return deleteDocuments(collectionName, filter);
    }
    
    /**
     * Count documents using a filter map
     * @param collectionName The collection name
     * @param filterMap Map of field names to their filter values
     * @return The count of matching documents
     */
    public long countByFilterMap(String collectionName, Map<String, Object> filterMap) {
        Bson filter = getFilter(filterMap);
        return countDocuments(collectionName, filter);
    }

    /**
     * Create a filter from a map of field names and values
     * @param filterMap Map of field names to their filter values
     * @return A Bson filter that can be used in MongoDB queries
     */
    public Bson getFilter(Map<String, Object> filterMap) {
        if (filterMap == null || filterMap.isEmpty()) {
            // Return a filter that matches all documents
            return new Document();
        }
        
        List<Bson> filters = new ArrayList<>();
        
        for (Map.Entry<String, Object> entry : filterMap.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            
            // Handle special case for ObjectId
            if (key.equals("_id") && value instanceof String) {
                try {
                    filters.add(Filters.eq(key, new ObjectId(value.toString())));
                } catch (IllegalArgumentException e) {
                    // If the string is not a valid ObjectId, use it as is
                    filters.add(Filters.eq(key, value));
                }
            } 
            // Handle null values
            else if (value == null) {
                filters.add(Filters.eq(key, null));
            }
            // Handle string values that might need regex
            else if (value instanceof String && ((String) value).contains("*")) {
                String regex = ((String) value).replace("*", ".*");
                filters.add(Filters.regex(key, regex, "i")); // "i" for case-insensitive
            }
            // Handle range queries with maps
            else if (value instanceof Map) {
                Map<String, Object> rangeMap = (Map<String, Object>) value;
                for (Map.Entry<String, Object> rangeEntry : rangeMap.entrySet()) {
                    String op = rangeEntry.getKey();
                    Object val = rangeEntry.getValue();
                    
                    switch (op) {
                        case "$gt":
                            filters.add(Filters.gt(key, val));
                            break;
                        case "$gte":
                            filters.add(Filters.gte(key, val));
                            break;
                        case "$lt":
                            filters.add(Filters.lt(key, val));
                            break;
                        case "$lte":
                            filters.add(Filters.lte(key, val));
                            break;
                        case "$ne":
                            filters.add(Filters.ne(key, val));
                            break;
                        case "$in":
                            if (val instanceof List) {
                                filters.add(Filters.in(key, (List<?>) val));
                            }
                            break;
                        case "$nin":
                            if (val instanceof List) {
                                filters.add(Filters.nin(key, (List<?>) val));
                            }
                            break;
                        default:
                            // Ignore unknown operators
                            break;
                    }
                }
            }
            // Default case: exact match
            else {
                filters.add(Filters.eq(key, value));
            }
        }
        
        // If there's only one filter, return it directly
        if (filters.size() == 1) {
            return filters.get(0);
        }
        
        // Otherwise, combine all filters with AND
        return Filters.and(filters);
    }

    /**
     * Find payments for a specific house by month and year
     * @param houseId The house ID to find payments for
     * @param month The month to filter by
     * @param year The year to filter by
     * @return List of Payment objects matching the criteria
     */
    public List<Payment> findPaymentsByHouseIdMonthAndYear(Long houseId, int month, int year) {
        MongoCollection<Document> collection = getCollection("payment_records");
        
        // Create the filter to find the payment record for the house
        Bson filter = Filters.and(
            Filters.eq("houseId", houseId),
            Filters.eq("payments.month", month),
            Filters.eq("payments.year", year)
        );
        
        return getPayments(month, year, collection, filter);
    }

    private List<Payment> getPayments(int month, int year, MongoCollection<Document> collection, Bson filter) {
        // Find the payment record
        FindIterable<Document> paymentRecord = collection.find(filter);
        
        if (paymentRecord == null) {
            return new ArrayList<>();
        }
        
        // Extract the payments array
        List<Document> paymentDocs = new ArrayList<>();
        for (Document doc : paymentRecord) {
            List<Document> payments = (List<Document>) doc.get("payments");
            if (payments != null && !payments.isEmpty()) {
                logger.info("Retrieved payments From DB: {}", payments);
                paymentDocs.addAll(payments);
            }
        }
        
        // Filter and convert to Payment objects
        List<Payment> payments = new ArrayList<>();
        for (Document doc : paymentDocs) {
            if (doc.getInteger("month") == month && doc.getInteger("year") == year) {
                Payment payment = new Payment();
                payment.setId(doc.getObjectId("_id") != null ? doc.getObjectId("_id").toString() : null);
                payment.setAmtPayable(doc.getDouble("amtPayable"));
                payment.setAmtPaid(doc.getDouble("amtPaid"));
                payment.setComments(doc.getString("comments"));
                payment.setMonth(doc.getInteger("month"));
                payment.setYear(doc.getInteger("year"));                
                // Convert Date to LocalDateTime
                payment.setCreatedAt(convertToLocalDateTime(doc.getDate("createdAt")));
                payment.setModifiedAt(convertToLocalDateTime(doc.getDate("modifiedAt")));
                
                payments.add(payment);
                logger.info("Added payment: {}", payment);
            }
        }
        
        return payments;
    }

    /**
     * Update a specific maintenance record in a monthly maintenance document
     * @param maintenanceId The monthly maintenance document ID
     * @param houseId The house ID to identify the specific record
     * @param field The field within the maintenance record to update
     * @param value The new value for the field
     * @return The update result
     */
    public UpdateResult updateMaintenanceRecordByHouseId(String maintenanceId, Long houseId, 
                                                        String field, Object value) {
        MongoCollection<Document> collection = getCollection("monthly_maintenance");
        
        // Create the filter to find the monthly maintenance document
        Bson filter = Filters.eq("_id", new ObjectId(maintenanceId));
        
        // Create the update operation with positional filtered operator
        String updatePath = "monthlyMaintenanceRecords.$[element]." + field;
        Bson update = Updates.set(updatePath, value);
        
        // Create the array filter for the specific house ID
        Bson arrayFilter = Filters.eq("element.houseId", houseId);
        
        // Create update options with array filters
        com.mongodb.client.model.UpdateOptions options = new com.mongodb.client.model.UpdateOptions()
            .arrayFilters(List.of(arrayFilter));
        
        // Execute the update
        return collection.updateOne(filter, update, options);
    }

    /**
     * Update multiple fields in a specific maintenance record
     * @param maintenanceId The monthly maintenance document ID
     * @param houseId The house ID to identify the specific record
     * @param updates Map of field names to their new values
     * @return The update result
     */
    public UpdateResult updateMaintenanceRecordFields(String maintenanceId, Long houseId, 
                                                     Map<String, Object> updates) {
        MongoCollection<Document> collection = getCollection("monthly_maintenance");
        
        // Create the filter to find the monthly maintenance document
        Bson filter = Filters.eq("_id", new ObjectId(maintenanceId));
        
        // Create a list of update operations
        List<Bson> updateOperations = new ArrayList<>();
        for (Map.Entry<String, Object> entry : updates.entrySet()) {
            String updatePath = "monthlyMaintenanceRecords.$[element]." + entry.getKey();
            updateOperations.add(Updates.set(updatePath, entry.getValue()));
        }
        
        // Combine all update operations
        Bson combinedUpdate = Updates.combine(updateOperations);
        
        // Create the array filter for the specific house ID
        Bson arrayFilter = Filters.eq("element.houseId", houseId);
        
        // Create update options with array filters
        com.mongodb.client.model.UpdateOptions options = new com.mongodb.client.model.UpdateOptions()
            .arrayFilters(List.of(arrayFilter));
        
        // Execute the update
        return collection.updateOne(filter, combinedUpdate, options);
    }

    /**
     * Convert java.util.Date to java.time.LocalDateTime
     * @param date The Date object to convert
     * @return The converted LocalDateTime object, or null if date is null
     */
    private LocalDateTime convertToLocalDateTime(java.util.Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant()
            .atZone(java.time.ZoneId.systemDefault())
            .toLocalDateTime();
    }

    public List<Payment> findPaymentsByMonthAndYear(int month, int year) {
        MongoCollection<Document> collection = getCollection("payment_records");
        
        // Create the filter to find the payment record by month and year
        Bson filter = Filters.and(
            Filters.eq("payments.month", month),
            Filters.eq("payments.year", year)
        );
        
        return getPayments(month, year, collection, filter);
    }
}


