package prs.lrh.maintenance.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import prs.lrh.maintenance.model.HouseRecord;
import prs.lrh.maintenance.model.Payment;
import prs.lrh.maintenance.model.PaymentRecord;
import prs.lrh.maintenance.repository.HouseRepository;
import prs.lrh.maintenance.repository.PaymentRecordRepository;
import prs.lrh.maintenance.service.MongoDBService;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Controller
@RequestMapping("/payments")
public class PaymentController {
    
    private static final Logger logger = LoggerFactory.getLogger(PaymentController.class);

    @Autowired
    private PaymentRecordRepository paymentRecordRepository;
    
    @Autowired
    private HouseRepository houseRepository;

    @Autowired
    private MongoDBService mongoDBService;

    @GetMapping
    public String listPayments(Model model, 
                              @RequestParam(required = false) Long houseId,
                              @RequestParam(required = false) String houseNumber) {
        
        logger.debug("Listing payments with filters - houseId: {}, houseNumber: {}", houseId, houseNumber);
        
        List<PaymentRecord> paymentRecords;
        
        if (houseId != null) {
            logger.debug("Searching for payment records by houseId: {}", houseId);
            Optional<PaymentRecord> record = paymentRecordRepository.findByHouseId(houseId);
            paymentRecords = record.map(List::of).orElse(List.of());
        } else if (houseNumber != null && !houseNumber.isEmpty()) {
            logger.debug("Searching for payment records by houseNumber: {}", houseNumber);
            Optional<PaymentRecord> record = paymentRecordRepository.findByHouseNumber(houseNumber);
            paymentRecords = record.map(List::of).orElse(List.of());
        } else {
            logger.debug("Retrieving all payment records");
            paymentRecords = paymentRecordRepository.findAll();
        }
        
        logger.info("Found {} payment records", paymentRecords.size());
        model.addAttribute("paymentRecords", paymentRecords);
        model.addAttribute("houses", houseRepository.findAll());
        return "payments";
    }
    
    @GetMapping("/{id}")
    public String viewPaymentRecord(@PathVariable String id, Model model) {
        logger.debug("Viewing payment record with id: {}", id);
        Optional<PaymentRecord> paymentRecord = paymentRecordRepository.findById(id);
        
        if (paymentRecord.isPresent()) {
            logger.debug("Payment record found: {}", paymentRecord.get().getId());
            model.addAttribute("paymentRecord", paymentRecord.get());
            return "payment-detail";
        } else {
            logger.warn("Payment record not found with id: {}", id);
            return "redirect:/payments";
        }
    }
    
    @GetMapping("/new")
    public String newPaymentForm(Model model, @RequestParam(required = false) Long houseId,
     @RequestParam(required = false) Double amtPayable) {
        logger.debug("New payment form - houseId: {}, amtPayable: {}", houseId, amtPayable);
        
        model.addAttribute("houses", houseRepository.findAll());
        model.addAttribute("selectedHouseId", houseId);
        if (amtPayable != null && amtPayable > 0) {
            model.addAttribute("defaultAmtPayable", amtPayable);
        }

        // If houseId is provided, fetch the house and pass its balance as default amtPayable
        if (houseId != null && amtPayable == null) {
            Optional<HouseRecord> house = houseRepository.findById(houseId);
            if (house.isPresent()) {
                logger.debug("Setting default amount payable to house balance: {}", house.get().getBalance());
                model.addAttribute("defaultAmtPayable", house.get().getBalance());
            }
        }
        
        return "payment-new";
    }
    
    @PostMapping("/add")
    public String addPayment(@RequestParam Long houseId,
                            @RequestParam double amtPayable,
                            @RequestParam double amtPaid,
                            @RequestParam String comments,
                            @RequestParam int month,
                            @RequestParam int year) {
        
        logger.info("Adding new payment - houseId: {}, amount: {}, month: {}/{}", 
                   houseId, amtPaid, month, year);
        
        Optional<HouseRecord> house = houseRepository.findById(houseId);
        if (house.isEmpty()) {
            logger.error("House not found with id: {}", houseId);
            return "redirect:/payments";
        }
        
        // Create new payment
        Payment payment = new Payment();
        payment.setAmtPayable(amtPayable);
        payment.setAmtPaid(amtPaid);
        payment.setComments(comments);
        payment.setMonth(month);
        payment.setYear(year);
        payment.setCreatedAt(LocalDateTime.now());
        payment.setModifiedAt(LocalDateTime.now());
        
        // Check if payment record exists for this house
        Optional<PaymentRecord> existingRecord = paymentRecordRepository.findByHouseId(houseId);
        
        if (existingRecord.isEmpty()) {
            // Create a new payment record if one doesn't exist
            logger.debug("No existing payment record found for house {}. Creating new record.", houseId);
            PaymentRecord newRecord = new PaymentRecord();
            newRecord.setHouseId(houseId);
            newRecord.setHouseNumber(house.get().getHouseNumber());
            newRecord.setPayments(List.of(payment));
            paymentRecordRepository.save(newRecord);
            logger.info("Created new payment record for house: {}", houseId);
        } else {
            // Add payment to existing record
            logger.debug("Adding payment to existing record for house: {}", houseId);
            mongoDBService.addPaymentToRecord(houseId, payment);
            logger.info("Payment added to existing record for house: {}", houseId);
        }
        
        // Update house balance by subtracting the amount paid
        HouseRecord houseRecord = house.get();
        double oldBalance = houseRecord.getBalance();
        double newBalance = oldBalance - amtPaid;
        
        logger.debug("Updating house balance from {} to {}", oldBalance, newBalance);
        Map<String, Object> filter = Map.of("_id", houseRecord.getId());
        Map<String, Object> updates = Map.of("balance", newBalance);
        mongoDBService.updateByFilterMap("house_records", filter, updates);
        logger.info("House balance updated for house: {}", houseId);
        
        return "redirect:/houses";
    }
}




