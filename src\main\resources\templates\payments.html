<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:replace="~{fragments/layout :: head('Payment Records')}">
</head>
<body>
    <div th:replace="~{fragments/layout :: header}"></div>
    
    <div class="container">
        
        <div class="card mb-4">
            <div class="card-header">Search Payment Records</div>
            <div class="card-body">
                <form th:action="@{/payments}" method="get" class="row g-3">
                    <div class="col-md-5">
                        <label for="houseId" class="form-label">House ID</label>
                        <input type="number" class="form-control" id="houseId" name="houseId" placeholder="Enter House ID">
                    </div>
                    <div class="col-md-5">
                        <label for="houseNumber" class="form-label">House Number</label>
                        <input type="text" class="form-control" id="houseNumber" name="houseNumber" placeholder="Enter House Number">
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">Search</button>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="mb-3">
            <a th:href="@{/payments}" class="btn btn-outline-secondary">Clear Search</a>
        </div>
        
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>House ID</th>
                    <th>House Number</th>
                    <th>Number of Payments</th>
                    <th>Total Payable</th>
                    <th>Total Paid</th>
                    <th>Balance</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <tr th:each="record : ${paymentRecords}">
                    <td th:text="${record.houseId}"></td>
                    <td th:text="${record.houseNumber}"></td>
                    <td th:text="${record.payments.size()}"></td>
                    <td th:text="${#aggregates.sum(record.payments.![amtPayable])}"></td>
                    <td th:text="${#aggregates.sum(record.payments.![amtPaid])}"></td>
                    <td th:text="${#aggregates.sum(record.payments.![amtPayable]) - #aggregates.sum(record.payments.![amtPaid])}"></td>
                    <td>
                        <a th:href="@{/payments/{id}(id=${record.id})}" class="btn btn-sm btn-info">View Details</a>
                    </td>
                </tr>
                <tr th:if="${#lists.isEmpty(paymentRecords)}">
                    <td colspan="7" class="text-center">No payment records found</td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div th:replace="~{fragments/layout :: footer}"></div>
</body>
</html>
