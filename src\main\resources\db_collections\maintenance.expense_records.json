[{"_id": {"$oid": "681b78df853f89992dd5be0d"}, "month": 5, "year": 2025, "monthlyExpenses": [{"_id": "bfa403ad-cbc8-4d16-b771-37926ae55049", "amount": 32000, "category": "Security", "subCategory": "Salary", "comments": "April Month Agency payment", "expenseDate": {"$date": "2025-05-02T05:15:00.000Z"}, "updatedOn": {"$date": "2025-05-07T17:17:21.911Z"}}, {"_id": "004a76ce-3e46-436f-81f1-64586bef92a1", "amount": 17000, "category": "Watchman", "subCategory": "Salary", "comments": "<PERSON><PERSON> April salary payment", "expenseDate": {"$date": "2025-05-02T17:15:00.000Z"}, "updatedOn": {"$date": "2025-05-07T17:16:31.535Z"}}, {"_id": "d37b3576-e09b-48a1-845b-933745c67fe2", "amount": 7000, "category": "Cleaning", "subCategory": "Garbage Collection", "comments": "April month payment - Cash", "expenseDate": {"$date": "2025-05-05T19:13:00.000Z"}, "updatedOn": {"$date": "2025-05-08T07:16:31.359Z"}}, {"_id": "ec88a3af-50d4-4844-94a8-f71e945c093c", "amount": 9500, "category": "Cleaning", "subCategory": "Gardener <PERSON>", "comments": "April month salary, after deducting 4000 from 11k advance salary - 7k pending", "expenseDate": {"$date": "2025-05-08T07:16:00.000Z"}, "updatedOn": {"$date": "2025-05-08T07:19:19.455Z"}}, {"_id": "6929aa95-7e5c-4d9f-ad7f-423690386772", "amount": 3000, "category": "Civil Work", "subCategory": "Repair Charges", "comments": "Wielding changes towards fixing tin sheet on Community Hall steps", "expenseDate": {"$date": "2025-05-06T07:19:00.000Z"}, "updatedOn": {"$date": "2025-05-08T07:20:45.717Z"}}, {"_id": "8a5af7ba-9ccb-414e-9241-3c60b5bc5110", "amount": 8896, "category": "Electrical ", "subCategory": "Bill of USC 110985133", "comments": "April month bill payment - GPay", "expenseDate": {"$date": "2025-05-08T07:20:00.000Z"}, "updatedOn": {"$date": "2025-05-08T07:28:54.565Z"}}, {"_id": "d70b412d-388f-4424-8393-1ca66a893c22", "amount": 23004, "category": "Electrical ", "subCategory": "Bill of USC 108978267", "comments": "April month bill payment - GPay", "expenseDate": {"$date": "2025-05-08T07:28:00.000Z"}, "updatedOn": {"$date": "2025-05-08T07:30:01.026Z"}}, {"_id": "e4c67181-1938-4278-9f2b-2b2142c1ee87", "amount": 300, "category": "Electrical ", "subCategory": "Electirician Charges", "comments": "Transfer fuse repair done by line in-charge", "expenseDate": {"$date": "2025-05-10T13:32:00.000Z"}, "updatedOn": {"$date": "2025-05-11T13:33:45.589Z"}}, {"_id": "4ded3903-9b47-4b51-a76f-d2fde92a1999", "amount": 450, "category": "Cleaning", "subCategory": "Labour Charges", "comments": "Community hall restrooms cleaning", "expenseDate": {"$date": "2025-05-18T15:55:00.000Z"}, "updatedOn": {"$date": "2025-05-24T10:26:27.840Z"}}, {"_id": "fcae1511-8aa3-4617-a679-6c5d6ffe87a4", "amount": 400, "category": "Electrical ", "subCategory": "Electirician Charges", "comments": "Electrical repair done in second lane and third lane by <PERSON><PERSON><PERSON>", "expenseDate": {"$date": "2025-05-26T21:34:00.000Z"}, "updatedOn": {"$date": "2025-05-26T16:05:35.810Z"}}, {"_id": "9726b8c3-a94c-470d-9f7c-4800104c378b", "amount": 600, "category": "Cleaning", "subCategory": "Cleaning Material", "comments": "Brooms purchased by <PERSON><PERSON>", "expenseDate": {"$date": "2025-05-30T07:09:00.000Z"}, "updatedOn": {"$date": "2025-05-31T07:10:01.936Z"}}], "createdAt": {"$date": "2025-05-07T15:14:39.604Z"}, "modifiedAt": {"$date": "2025-05-31T07:10:01.936Z"}, "_class": "prs.lrh.maintenance.model.ExpenseRecord"}, {"_id": {"$oid": "681b7990a54df1f4b48b2870"}, "month": 4, "year": 2025, "monthlyExpenses": [{"_id": "6f7fe0b0-e27d-495a-b2cd-66eb7c2203ee", "amount": 17600, "category": "Civil Work", "subCategory": "Construction Material", "comments": "14 ton robo sand and 12.8 ton 10mm concrete stones", "expenseDate": {"$date": "2025-04-04T14:07:00.000Z"}, "updatedOn": {"$date": "2025-05-08T14:08:40.605Z"}}, {"_id": "f42d74d4-0e2a-4e01-89b9-e4ba81175ebd", "amount": 700, "category": "Cleaning", "subCategory": "Cleaning Material", "comments": "Purchase of brooms and tea for labour. Payment done to <PERSON><PERSON>", "expenseDate": {"$date": "2025-04-04T14:08:00.000Z"}, "updatedOn": {"$date": "2025-05-08T14:09:08.463Z"}}, {"_id": "281aaaa8-a479-4217-a767-98f7e4b251fc", "amount": 7000, "category": "Cleaning", "subCategory": "Garbage Collection", "comments": "March month payment to garbage collection", "expenseDate": {"$date": "2025-04-01T14:09:00.000Z"}, "updatedOn": {"$date": "2025-05-08T14:10:05.517Z"}}, {"_id": "56350271-8f9c-4b5d-9b82-06f2fc4c249e", "amount": 9500, "category": "Cleaning", "subCategory": "Gardener <PERSON>", "comments": "March month salary, after deducting 4000 from 15k advance salary - 11k pending", "expenseDate": {"$date": "2025-04-04T14:10:00.000Z"}, "updatedOn": {"$date": "2025-05-08T14:10:38.929Z"}}, {"_id": "1a04223d-414a-4e00-b5b8-aaf3df3f219d", "amount": 21350, "category": "Electrical ", "subCategory": "Bill of USC 108978267", "comments": "March month electricity bill payment", "expenseDate": {"$date": "2025-04-04T14:10:00.000Z"}, "updatedOn": {"$date": "2025-05-08T14:11:23.590Z"}}, {"_id": "cfcecb46-41f3-4e7f-8d90-c56ee4095358", "amount": 23314, "category": "Electrical ", "subCategory": "Bill of USC 110985133", "comments": "Payment of January and February month pending bills due to new meter replacement", "expenseDate": {"$date": "2025-04-20T14:11:00.000Z"}, "updatedOn": {"$date": "2025-05-08T14:12:07.563Z"}}, {"_id": "870a83e5-c399-49b7-ae66-c3b3b75534b1", "amount": 14156, "category": "Electrical ", "subCategory": "Bill of USC 110985133", "comments": "March month bill", "expenseDate": {"$date": "2025-04-07T14:12:00.000Z"}, "updatedOn": {"$date": "2025-05-08T14:14:08.491Z"}}, {"_id": "99d51da0-8990-4fb0-abfe-fd80a6cfa53f", "amount": 452, "category": "Electrical ", "subCategory": "Electircity Charges", "comments": "Electricity connection usage for last lane bore. Pa<PERSON> to <PERSON> uncle for 69 house line", "expenseDate": {"$date": "2025-04-07T14:12:00.000Z"}, "updatedOn": {"$date": "2025-05-08T14:13:47.400Z"}}, {"_id": "078ff1c6-8899-45e7-b9b9-53f72466ad95", "amount": 2737, "category": "Civil Work", "subCategory": "Repair Charges", "comments": "Repair cost for house number 15, due to damage of water pipes done by community hall tins broken during strom", "expenseDate": {"$date": "2025-04-18T14:14:00.000Z"}, "updatedOn": {"$date": "2025-05-08T14:15:10.031Z"}}, {"_id": "7a4570d2-483f-4738-bffd-e775536a5910", "amount": 32000, "category": "Security", "subCategory": "Salary", "comments": "March month security agency payment", "expenseDate": {"$date": "2025-04-04T14:15:00.000Z"}, "updatedOn": {"$date": "2025-05-08T14:15:43.517Z"}}, {"_id": "209874f3-75cf-4c24-b0d7-398d2637dc6b", "amount": 900, "category": "Security", "subCategory": "Drinking Water", "comments": "Three months drinking water bubbles bill payment", "expenseDate": {"$date": "2025-04-09T14:15:00.000Z"}, "updatedOn": {"$date": "2025-05-08T14:16:13.728Z"}}, {"_id": "8a3c8956-66b5-407b-bb73-612ee82dfac3", "amount": 17000, "category": "Watchman", "subCategory": "Salary", "comments": "March month salary paid to <PERSON><PERSON> <PERSON> <PERSON><PERSON>", "expenseDate": {"$date": "2025-04-02T14:16:00.000Z"}, "updatedOn": {"$date": "2025-05-08T14:17:15.258Z"}}, {"_id": "a18585ac-8d27-407a-aabd-633324aaf3de", "amount": 7750, "category": "Security", "subCategory": "Solar Fencing", "comments": "Repair charges paid to 9553804676 - GPay", "expenseDate": {"$date": "2025-04-20T14:17:00.000Z"}, "updatedOn": {"$date": "2025-05-08T14:18:48.123Z"}}, {"_id": "4c851680-74ba-4234-bb67-6271fdea7407", "amount": 230, "category": "Security", "subCategory": "Stationary", "comments": "Registry purchased by <PERSON><PERSON>", "expenseDate": {"$date": "2025-04-20T14:18:00.000Z"}, "updatedOn": {"$date": "2025-05-08T14:19:40.060Z"}}, {"_id": "33446837-6faa-4bf9-88e3-af158bf09bad", "amount": 13180, "category": "Civil Work", "subCategory": "Construction Material", "comments": "Cement bags amount paid to <PERSON><PERSON><PERSON>", "expenseDate": {"$date": "2025-04-25T14:19:00.000Z"}, "updatedOn": {"$date": "2025-05-08T14:20:35.152Z"}}, {"_id": "83f48ced-4ce1-4596-93cd-01624c88c8e0", "amount": 10000, "category": "Civil Work", "subCategory": "Labour Charges", "comments": "Related to cleaning and repairing Basketball court and open area in North-east park", "expenseDate": {"$date": "2025-04-25T14:20:00.000Z"}, "updatedOn": {"$date": "2025-05-08T14:22:10.992Z"}}, {"_id": "ab7202b1-2f5b-4c85-9202-63de4950bd54", "amount": 400, "category": "Electrical ", "subCategory": "Electirician Charges", "comments": "Fixing 69 house MCB (as used for last lane bore)", "expenseDate": {"$date": "2025-04-28T14:22:00.000Z"}, "updatedOn": {"$date": "2025-05-08T14:23:40.186Z"}}, {"_id": "c801784a-921a-4ba1-9fbc-463b6e7bc314", "amount": 1500, "category": "Civil Work", "subCategory": "Repair Charges", "comments": "Carpenter charges for H.No.34 damage fixing", "expenseDate": {"$date": "2025-04-29T14:23:00.000Z"}, "updatedOn": {"$date": "2025-05-08T14:24:34.247Z"}}], "createdAt": {"$date": "2025-05-07T15:14:39.604Z"}, "modifiedAt": {"$date": "2025-05-08T14:24:34.247Z"}, "_class": "prs.lrh.maintenance.model.ExpenseRecord"}]