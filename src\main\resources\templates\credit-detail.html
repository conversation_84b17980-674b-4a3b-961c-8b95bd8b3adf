<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:replace="~{fragments/layout :: head('Credit Detail')}">
</head>
<body>
    <div th:replace="~{fragments/layout :: header}"></div>
    
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Credit Details</h1>
            <div>
                <button id="exportPdfBtn" class="btn btn-secondary mr-2">Export as PDF</button>
                <a th:href="@{/credits}" class="btn btn-secondary">Back to Credits</a>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    Credit Record for 
                    <span th:text="${credit.month == 1 ? 'January' : 
                                    credit.month == 2 ? 'February' : 
                                    credit.month == 3 ? 'March' : 
                                    credit.month == 4 ? 'April' : 
                                    credit.month == 5 ? 'May' : 
                                    credit.month == 6 ? 'June' : 
                                    credit.month == 7 ? 'July' : 
                                    credit.month == 8 ? 'August' : 
                                    credit.month == 9 ? 'September' : 
                                    credit.month == 10 ? 'October' : 
                                    credit.month == 11 ? 'November' : 'December'}"></span>
                    <span th:text="${credit.year}"></span>
                </h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-4">
                        <p><strong>Total Credits:</strong> <span th:text="${credit.monthlyCredits != null ? credit.monthlyCredits.size() : 0}"></span></p>
                    </div>
                    <div class="col-md-4">
                        <p><strong>Total Amount:</strong> <span th:text="${'Rs.' + #numbers.formatDecimal(totalAmount, 1, 'COMMA', 2, 'POINT')}"></span></p>
                    </div>
                    <div class="col-md-4">
                        <p><strong>Last Modified:</strong> <span th:text="${#temporals.format(credit.modifiedAt, 'dd-MM-yyyy HH:mm')}"></span></p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">Add New Credit</div>
            <div class="card-body">
                <form th:action="@{/credits/add-item}" method="post" class="row gy-3" id="creditForm">
                    <input type="hidden" name="creditId" th:value="${credit.id}">
                    <input type="hidden" name="itemId" id="itemId" value="">
                    <input type="hidden" name="editMode" id="editMode" value="false">
                    
                    <div class="col-md-6">
                        <div class="input-group">
                            <span class="input-group-text" style="width: 120px;">Amount</span>
                            <input type="number" step="0.01" class="form-control" id="amount" name="amount" required>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="input-group">
                            <span class="input-group-text" style="width: 120px;">Date</span>
                            <input type="datetime-local" class="form-control" id="creditDate" name="creditDate" required>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="input-group">
                            <span class="input-group-text" style="width: 120px;">Category</span>
                            <select class="form-select" id="category" name="category" required onchange="loadSubCategories()">
                                <option value="">Select Category</option>
                                <option th:each="cat : ${creditCategories}" 
                                        th:value="${cat.category}" 
                                        th:text="${cat.category}"
                                        th:data-subcategories="${#strings.listJoin(cat.subCategory, ',')}"></option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="input-group">
                            <span class="input-group-text" style="width: 120px;">Sub-Category</span>
                            <select class="form-select" id="subCategory" name="subCategory" required>
                                <option value="">Select Sub-Category</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="col-md-12">
                        <div class="input-group">
                            <span class="input-group-text" style="width: 120px;">Comments</span>
                            <input type="text" class="form-control" id="comments" name="comments">
                        </div>
                    </div>
                    
                    <div class="col-12 mt-3">
                        <button type="submit" class="btn btn-primary" id="submitBtn">Add Credit</button>
                        <button type="button" class="btn btn-secondary" id="cancelBtn" style="display:none;" onclick="cancelEdit()">Cancel</button>
                    </div>
                </form>
            </div>
        </div>

        <div class="card">
            <div class="card-header">Credit Items</div>
            <div class="card-body">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th style="min-width: 12ch;">Date</th>
                            <th>Amount</th>
                            <th>Category</th>
                            <th>Sub-Category</th>
                            <th>Comments</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr th:each="item, stat : ${credit.monthlyCredits}">
                            <td th:text="${#temporals.format(item.creditDate, 'dd-MM-yyyy')}"></td>
                            <td th:text="${'Rs.' + #numbers.formatDecimal(item.amount, 1, 'COMMA', 2, 'POINT')}"></td>
                            <td th:text="${item.category}"></td>
                            <td th:text="${item.subCategory}"></td>
                            <td th:text="${item.comments}"></td>
                            <td>
                                <button class="btn btn-sm btn-primary me-1 edit-credit-btn" 
                                        th:data-index="${stat.index}"
                                        th:data-id="${item.id}"
                                        th:data-amount="${item.amount}"
                                        th:data-credit-date="${#temporals.format(item.creditDate, 'yyyy-MM-dd''T''HH:mm')}"
                                        th:data-category="${item.category}"
                                        th:data-subcategory="${item.subCategory}"
                                        th:data-comments="${item.comments != null ? item.comments : ''}">
                                    Edit
                                </button>
                                <a th:href="@{/credits/delete-item(creditId=${credit.id}, itemId=${item.id})}" 
                                   class="btn btn-sm btn-danger"
                                   onclick="return confirm('Are you sure you want to delete this credit item?')">
                                    Delete
                                </a>
                            </td>
                        </tr>
                        <tr th:if="${credit.monthlyCredits == null || credit.monthlyCredits.isEmpty()}">
                            <td colspan="6" class="text-center">No credit items found</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>        
    </div>
    
    <div th:replace="~{fragments/layout :: footer}"></div>
    
    <script>
        function loadSubCategories() {
            const categorySelect = document.getElementById('category');
            const subCategorySelect = document.getElementById('subCategory');
            
            // Clear existing options
            subCategorySelect.innerHTML = '<option value="">Select Sub-Category</option>';
            
            if (categorySelect.selectedIndex > 0) {
                const selectedOption = categorySelect.options[categorySelect.selectedIndex];
                const subCategories = selectedOption.getAttribute('data-subcategories');
                
                if (subCategories) {
                    const subCategoryArray = subCategories.split(',');
                    
                    subCategoryArray.forEach(subCategory => {
                        const option = document.createElement('option');
                        option.value = subCategory.trim();
                        option.textContent = subCategory.trim();
                        subCategorySelect.appendChild(option);
                    });
                }
            }
        }
        
        function editCreditItem(button) {
            // Get data from data attributes
            const index = button.getAttribute('data-index');
            const id = button.getAttribute('data-id');
            const amount = button.getAttribute('data-amount');
            const creditDate = button.getAttribute('data-credit-date');
            const category = button.getAttribute('data-category');
            const subCategory = button.getAttribute('data-subcategory');
            const comments = button.getAttribute('data-comments');
            
            // Set form to edit mode
            document.getElementById('editMode').value = 'true';
            document.getElementById('itemId').value = id;
            
            // Fill form with item data
            document.getElementById('amount').value = amount;
            document.getElementById('creditDate').value = creditDate;
            document.getElementById('comments').value = comments;
            
            // Set category
            const categorySelect = document.getElementById('category');
            for (let i = 0; i < categorySelect.options.length; i++) {
                if (categorySelect.options[i].value === category) {
                    categorySelect.selectedIndex = i;
                    break;
                }
            }
            
            // Load subcategories
            loadSubCategories();
            
            // Set subcategory after subcategories are loaded
            setTimeout(() => {
                const subCategorySelect = document.getElementById('subCategory');
                for (let i = 0; i < subCategorySelect.options.length; i++) {
                    if (subCategorySelect.options[i].value === subCategory) {
                        subCategorySelect.selectedIndex = i;
                        break;
                    }
                }
            }, 100);
            
            // Change button text
            document.getElementById('submitBtn').textContent = 'Update Credit';
            document.getElementById('cancelBtn').style.display = 'inline-block';
            
            // Scroll to form
            document.getElementById('creditForm').scrollIntoView({ behavior: 'smooth' });
        }
        
        function cancelEdit() {
            // Reset form
            document.getElementById('creditForm').reset();
            document.getElementById('editMode').value = 'false';
            document.getElementById('itemId').value = '';
            
            // Reset subcategories
            document.getElementById('subCategory').innerHTML = '<option value="">Select Sub-Category</option>';
            
            // Reset button text
            document.getElementById('submitBtn').textContent = 'Add Credit';
            document.getElementById('cancelBtn').style.display = 'none';
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            // Set current date and time as default for new credits
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            
            document.getElementById('creditDate').value = `${year}-${month}-${day}T${hours}:${minutes}`;
            
            // Add event listeners to edit buttons
            document.querySelectorAll('.edit-credit-btn').forEach(button => {
                button.addEventListener('click', function() {
                    editCreditItem(this);
                });
            });
            
            // PDF Export functionality
            document.getElementById('exportPdfBtn').addEventListener('click', function() {
                const { jsPDF } = window.jspdf;
                const doc = new jsPDF();
                
                // Set document properties
                doc.setProperties({
                    title: 'Credit Details',
                    subject: 'Credit Record',
                    author: 'Maintenance System',
                    creator: 'Maintenance System'
                });
                
                // Get page dimensions
                const pageWidth = doc.internal.pageSize.getWidth();
                
                // Add title
                doc.setFontSize(16);
                doc.text('Credit Details', 14, 20);
                
                // Get month/year from the header
                const monthYear = document.querySelector('.card-header h5.mb-0').textContent.trim().replace('Credit Record for ', '');
                
                // Center the month/year
                doc.setFontSize(12);
                const monthYearWidth = doc.getStringUnitWidth(monthYear) * doc.internal.getFontSize() / doc.internal.scaleFactor;
                const monthYearX = (pageWidth - monthYearWidth) / 2;
                doc.text(monthYear, monthYearX, 30);
                
                // Add summary data
                const totalAmount = document.querySelector('.col-md-4:nth-child(2) p').textContent.split(':')[1].trim();
                
                doc.setFontSize(10);
                doc.text(`Total Amount: ${totalAmount}`, 14, 55);
                
                // Create table data from the HTML table
                const table = document.querySelector('.table');
                const headers = Array.from(table.querySelectorAll('thead th')).slice(0, 5).map(th => th.textContent);
                
                const rows = Array.from(table.querySelectorAll('tbody tr:not([th\\:if])')).map(tr => {
                    return Array.from(tr.querySelectorAll('td')).slice(0, 5).map(td => td.textContent.trim());
                });
                
                // Generate the table in the PDF
                doc.autoTable({
                    head: [headers],
                    body: rows,
                    startY: 75,
                    theme: 'grid',
                    styles: {
                        fontSize: 8,
                        cellPadding: 2
                    },
                    headStyles: {
                        fillColor: [66, 139, 202],
                        textColor: 255
                    },
                    alternateRowStyles: {
                        fillColor: [240, 240, 240]
                    }
                });
                
                // Add footer with date
                const now = new Date();
                const footer = `Generated on ${now.toLocaleDateString()} at ${now.toLocaleTimeString()}`;
                const pageCount = doc.internal.getNumberOfPages();
                
                for (let i = 1; i <= pageCount; i++) {
                    doc.setPage(i);
                    doc.setFontSize(8);
                    doc.text(footer, 14, doc.internal.pageSize.height - 10);
                    doc.text(`Page ${i} of ${pageCount}`, doc.internal.pageSize.width - 30, doc.internal.pageSize.height - 10);
                }
                
                // Save the PDF
                doc.save(`Credit_Details_${monthYear.replace(/\s+/g, '_')}.pdf`);
            });
        });
    </script>
</body>
</html>