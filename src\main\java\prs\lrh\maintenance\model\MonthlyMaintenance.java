package prs.lrh.maintenance.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

@Data
@Document(collection = "monthly_maintenance")
public class MonthlyMaintenance {
    @Id
    private String id;
    private int month;
    private int year;
    private List<MonthlyMaintenanceRecord> monthlyMaintenanceRecords;
}