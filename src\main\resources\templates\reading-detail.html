<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Reading Details</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Reading Details</h1>
            <a th:href="@{/readings}" class="btn btn-outline-secondary">Back to List</a>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    Reading for 
                    <span th:text="${reading.month == 1 ? 'January' : 
                                    reading.month == 2 ? 'February' : 
                                    reading.month == 3 ? 'March' : 
                                    reading.month == 4 ? 'April' : 
                                    reading.month == 5 ? 'May' : 
                                    reading.month == 6 ? 'June' : 
                                    reading.month == 7 ? 'July' : 
                                    reading.month == 8 ? 'August' : 
                                    reading.month == 9 ? 'September' : 
                                    reading.month == 10 ? 'October' : 
                                    reading.month == 11 ? 'November' : 'December'}"></span>
                    <span th:text="${reading.year}"></span>
                </h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <p><strong>Created At:</strong> <span th:text="${reading.createdAt}"></span></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Modified At:</strong> <span th:text="${reading.modifiedAt}"></span></p>
                    </div>
                </div>
            </div>
        </div>
        
        <h2>Water Readings</h2>
        <form th:action="@{/readings/update}" method="post" th:object="${waterReadingForm}">
            <input type="hidden" name="readingId" th:value="${reading.id}">
            <input type="hidden" name="month" th:value="${reading.month}">
            <input type="hidden" name="year" th:value="${reading.year}">
            
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>House ID</th>
                        <th>House Number</th>
                        <th>Previous Reading</th>
                        <th>Current Reading</th>
                        <th>Units</th>
                    </tr>
                </thead>
                <tbody>
                    <tr th:each="waterReading, stat : ${reading.waterReadings}" 
                        th:unless="${waterReading.currentReading == 0}">
                        <input type="hidden" name="waterReadings[0].id" th:name="'waterReadings[' + ${stat.index} + '].id'" th:value="${waterReading.id}">
                        <input type="hidden" name="waterReadings[0].houseId" th:name="'waterReadings[' + ${stat.index} + '].houseId'" th:value="${waterReading.houseId}">
                        <input type="hidden" name="waterReadings[0].houseNumber" th:name="'waterReadings[' + ${stat.index} + '].houseNumber'" th:value="${waterReading.houseNumber}">
                        <input type="hidden" name="waterReadings[0].prvReading" th:name="'waterReadings[' + ${stat.index} + '].prvReading'" th:value="${waterReading.prvReading}">
                        
                        <td th:text="${waterReading.houseId}"></td>
                        <td th:text="${waterReading.houseNumber}"></td>
                        <td th:text="${waterReading.prvReading}"></td>
                        <td>
                            <input type="number" class="form-control current-reading" 
                                   name="waterReadings[0].currentReading"
                                   th:name="'waterReadings[' + ${stat.index} + '].currentReading'" 
                                   th:value="${waterReading.currentReading}"
                                   th:data-prv="${waterReading.prvReading}"
                                   th:data-index="${stat.index}"
                                   onchange="calculateUnits(this)">
                        </td>
                        <td>
                            <input type="number" class="form-control units-field" 
                                   name="waterReadings[0].units"
                                   th:name="'waterReadings[' + ${stat.index} + '].units'" 
                                   th:value="${waterReading.units}" readonly>
                        </td>
                    </tr>
                    <tr th:if="${#lists.isEmpty(reading.waterReadings)}">
                        <td colspan="5" class="text-center">No water readings found</td>
                    </tr>
                </tbody>
            </table>
            
            <div class="d-flex justify-content-end mt-3 mb-5">
                <button type="submit" class="btn btn-primary">Update Readings</button>
            </div>
        </form>
        
        <script>
            function calculateUnits(input) {
                const currentReading = parseInt(input.value) || 0;
                const prvReading = parseInt(input.getAttribute('data-prv')) || 0;
                const index = input.getAttribute('data-index');
                
                // Calculate units (ensure it's not negative)
                const units = Math.max(0, currentReading - prvReading);
                
                // Update the units field
                document.querySelector('.units-field[name="waterReadings[' + index + '].units"]').value = units;
            }
            
            // Initialize units calculation for all readings
            document.addEventListener('DOMContentLoaded', function() {
                const currentReadings = document.querySelectorAll('.current-reading');
                currentReadings.forEach(input => calculateUnits(input));
            });
        </script>
    </div>
</body>
</html>




