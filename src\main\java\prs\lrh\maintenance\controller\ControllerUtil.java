package prs.lrh.maintenance.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.core.user.OAuth2User;

public class ControllerUtil {

    private static final Logger logger = LoggerFactory.getLogger(ControllerUtil.class);
    /**
     * Helper method to safely check if the current user is a SUPERUSER
     */
    public static String getUserRole(Authentication authentication) {
        try {
            if (authentication != null && authentication.getPrincipal() instanceof OAuth2User) {
                OAuth2User oauth2User = (OAuth2User) authentication.getPrincipal();
                String roleString = oauth2User.getAttribute("role");

                if (roleString != null) {
                    logger.info("User role: {}", roleString);
                } else {
                    logger.warn("Role not found in OAuth2 user attributes");
                }
                return roleString;
            } else {
                logger.warn("Authentication is null or not OAuth2User");
            }
        } catch (Exception e) {
            logger.error("Error checking user role: ", e);
        }
        return null;
    }
}
