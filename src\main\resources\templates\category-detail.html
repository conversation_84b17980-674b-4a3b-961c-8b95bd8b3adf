<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:replace="~{fragments/layout :: head('Edit Category')}">
</head>
<body>
    <div th:replace="~{fragments/layout :: header}"></div>
    
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Edit Category</h1>
            <a th:href="@{/categories}" class="btn btn-outline-secondary">Back to List</a>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Category Details</h5>
            </div>
            <div class="card-body">
                <form th:action="@{/categories/update}" method="post">
                    <input type="hidden" name="id" th:value="${category.id}">
                    
                    <div class="mb-3">
                        <label for="category" class="form-label">Category Name</label>
                        <input type="text" class="form-control" id="category" name="category" 
                               th:value="${category.category}" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="type" class="form-label">Type</label>
                        <select class="form-select" id="type" name="type" required>
                            <option value="">Select Type</option>
                            <option th:each="typeOption : ${types}" 
                                    th:value="${typeOption}" 
                                    th:text="${typeOption}"
                                    th:selected="${typeOption == category.type}"></option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="subCategories" class="form-label">Sub-Categories</label>
                        <textarea class="form-control" id="subCategories" name="subCategories" rows="3" required
                                  th:text="${#strings.listJoin(category.subCategory, ',')}"></textarea>
                        <div class="form-text">Enter sub-categories separated by commas (e.g., Salary, Water Supply, Device Charges)</div>
                    </div>
                    
                    <div class="mb-3">
                        <p><strong>Created At:</strong> <span th:text="${category.createdAt}"></span></p>
                        <p><strong>Last Modified:</strong> <span th:text="${category.modifiedAt}"></span></p>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">Update Category</button>
                </form>
            </div>
        </div>
    </div>
    
    <div th:replace="~{fragments/layout :: footer}"></div>
</body>
</html>