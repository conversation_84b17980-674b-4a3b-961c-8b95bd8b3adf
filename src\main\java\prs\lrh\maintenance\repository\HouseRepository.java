package prs.lrh.maintenance.repository;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;
import prs.lrh.maintenance.model.HouseRecord;

import java.util.List;

@Repository
public interface HouseRepository extends MongoRepository<HouseRecord, Long> {
    HouseRecord findByHouseNumber(String houseNumber);
    
    @Query("{'houseNumber': {$regex: ?0, $options: 'i'}}")
    List<HouseRecord> findByHouseNumberContainingIgnoreCase(String houseNumber);
    
    @Query("{'ownerName': {$regex: ?0, $options: 'i'}}")
    List<HouseRecord> findByOwnerNameContainingIgnoreCase(String ownerName);
    
    @Query("{'residentName': {$regex: ?0, $options: 'i'}}")
    List<HouseRecord> findByResidentNameContainingIgnoreCase(String residentName);
    
    // Add a method to find all houses sorted by ID
    List<HouseRecord> findAllByOrderByIdAsc();
}


