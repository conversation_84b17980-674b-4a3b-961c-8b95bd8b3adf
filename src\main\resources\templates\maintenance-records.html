<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:replace="~{fragments/layout :: head('Monthly Maintenance Records')}">
</head>
<body>
    <div th:replace="~{fragments/layout :: header}"></div>
    
    <div class="container mt-4">
        <h1>Monthly Maintenance Records</h1>
        
        <div class="card mb-4">
            <div class="card-header">Search Maintenance Records</div>
            <div class="card-body">
                <form th:action="@{/maintenance}" method="get" class="row g-3">
                    <div class="col-md-4">
                        <label for="month" class="form-label">Month</label>
                        <select class="form-select" id="month" name="month">
                            <option value="1" th:selected="${month == 1}">January</option>
                            <option value="2" th:selected="${month == 2}">February</option>
                            <option value="3" th:selected="${month == 3}">March</option>
                            <option value="4" th:selected="${month == 4}">April</option>
                            <option value="5" th:selected="${month == 5}">May</option>
                            <option value="6" th:selected="${month == 6}">June</option>
                            <option value="7" th:selected="${month == 7}">July</option>
                            <option value="8" th:selected="${month == 8}">August</option>
                            <option value="9" th:selected="${month == 9}">September</option>
                            <option value="10" th:selected="${month == 10}">October</option>
                            <option value="11" th:selected="${month == 11}">November</option>
                            <option value="12" th:selected="${month == 12}">December</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="year" class="form-label">Year</label>
                        <input type="number" class="form-select" id="year" name="year" 
                               th:value="${#calendars.format(#calendars.createNow(), 'yyyy')}" 
                               min="2020" max="2030">
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary">Search</button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Create button for current month if no records exist -->
        <div class="mb-4" th:if="${showCreateButton}">
            <div class="alert alert-info">
                <p>No maintenance records found for the current month.</p>
                <a th:href="@{/maintenance/create(month=${currentMonth},year=${currentYear})}" 
                   class="btn btn-success">
                    Create Maintenance Records For Current Month
                </a>
            </div>
        </div>
        
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>Month</th>
                    <th>Year</th>
                    <th>Number of Records</th>
                    <th>Total Amount</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <tr th:each="record : ${maintenanceRecords}">
                    <td th:text="${record.month == 1 ? 'January' : 
                                  record.month == 2 ? 'February' : 
                                  record.month == 3 ? 'March' : 
                                  record.month == 4 ? 'April' : 
                                  record.month == 5 ? 'May' : 
                                  record.month == 6 ? 'June' : 
                                  record.month == 7 ? 'July' : 
                                  record.month == 8 ? 'August' : 
                                  record.month == 9 ? 'September' : 
                                  record.month == 10 ? 'October' : 
                                  record.month == 11 ? 'November' : 'December'}"></td>
                    <td th:text="${record.year}"></td>
                    <td th:text="${record.monthlyMaintenanceRecords.size()}"></td>
                    <td th:text="${#aggregates.sum(record.monthlyMaintenanceRecords.![total])}"></td>
                    <td>
                        <a th:href="@{/maintenance/{id}(id=${record.id})}" class="btn btn-sm btn-info">View Details</a>
                    </td>
                </tr>
                <tr th:if="${#lists.isEmpty(maintenanceRecords) && !showCreateButton}">
                    <td colspan="5" class="text-center">No maintenance records found</td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div th:replace="~{fragments/layout :: footer}"></div>
</body>
</html>
