<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:replace="~{fragments/layout :: head('Payment Record Details')}">
</head>
<body>
    <div th:replace="~{fragments/layout :: header}"></div>
    
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Payment Record Details</h1>
            <a th:href="@{/payments}" class="btn btn-outline-secondary">Back to List</a>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    House Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <p><strong>House ID:</strong> <span th:text="${paymentRecord.houseId}"></span></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>House Number:</strong> <span th:text="${paymentRecord.houseNumber}"></span></p>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4">
                        <p><strong>Total Payable:</strong> <span th:text="${#aggregates.sum(paymentRecord.payments.![amtPayable])}"></span></p>
                    </div>
                    <div class="col-md-4">
                        <p><strong>Total Paid:</strong> <span th:text="${#aggregates.sum(paymentRecord.payments.![amtPaid])}"></span></p>
                    </div>
                    <div class="col-md-4">
                        <p><strong>Balance:</strong> <span th:text="${#aggregates.sum(paymentRecord.payments.![amtPayable]) - #aggregates.sum(paymentRecord.payments.![amtPaid])}"></span></p>
                    </div>
                </div>
            </div>
        </div>
        
        <h2>Payment History</h2>
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Amount Payable</th>
                    <th>Amount Paid</th>
                    <th>Balance</th>
                    <th>Comments</th>
                </tr>
            </thead>
            <tbody>
                <tr th:each="payment : ${paymentRecord.payments}">
                    <td th:text="${payment.month == 1 ? 'Jan' : 
                                  payment.month == 2 ? 'Feb' : 
                                  payment.month == 3 ? 'Mar' : 
                                  payment.month == 4 ? 'Apr' : 
                                  payment.month == 5 ? 'May' : 
                                  payment.month == 6 ? 'Jun' : 
                                  payment.month == 7 ? 'Jul' : 
                                  payment.month == 8 ? 'Aug' : 
                                  payment.month == 9 ? 'Sep' : 
                                  payment.month == 10 ? 'Oct' : 
                                  payment.month == 11 ? 'Nov' : 'Dec'} + ' ' + ${payment.year}"></td>
                    <td th:text="${payment.amtPayable}"></td>
                    <td th:text="${payment.amtPaid}"></td>
                    <td th:text="${payment.amtPayable - payment.amtPaid}"></td>
                    <td th:text="${payment.comments}"></td>
                </tr>
                <tr th:if="${#lists.isEmpty(paymentRecord.payments)}">
                    <td colspan="5" class="text-center">No payments found</td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div th:replace="~{fragments/layout :: footer}"></div>
</body>
</html>
