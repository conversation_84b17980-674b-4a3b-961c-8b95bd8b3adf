package prs.lrh.maintenance.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Document(collection = "monthly_readings")
public class MonthlyReading {
    @Id
    private String id;
    private int month;
    private int year;
    private LocalDateTime createdAt;
    private LocalDateTime modifiedAt;
    private List<WaterReading> waterReadings;
}