package prs.lrh.maintenance.model;

import lombok.Data;
import org.springframework.data.annotation.Id;

import java.time.LocalDateTime;

@Data
public class MonthlyMaintenanceRecord {
    @Id
    private String id;
    private Long houseId;
    private String houseNumber;
    private double fixedAmt;
    private double waterBill;
    private double otherCharges;
    private String comments;
    private double pastDues;
    private double garbage;
    private double total;
    private LocalDateTime createdAt;
    private LocalDateTime modifiedAt;
}