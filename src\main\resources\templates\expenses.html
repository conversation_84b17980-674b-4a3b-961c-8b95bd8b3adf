<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:replace="~{fragments/layout :: head('Expense Records')}">
</head>
<body>
    <div th:replace="~{fragments/layout :: header}"></div>
    
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Expense Records</h1>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">Search Expense Records</div>
            <div class="card-body">
                <form th:action="@{/expenses}" method="get" class="row g-3">
                    <div class="col-md-5">
                        <label for="month" class="form-label">Month</label>
                        <select class="form-select" id="month" name="month">
                            <option value="">Select Month</option>
                            <option value="1" th:selected="${param.month != null && param.month[0] == '1'}">January</option>
                            <option value="2" th:selected="${param.month != null && param.month[0] == '2'}">February</option>
                            <option value="3" th:selected="${param.month != null && param.month[0] == '3'}">March</option>
                            <option value="4" th:selected="${param.month != null && param.month[0] == '4'}">April</option>
                            <option value="5" th:selected="${param.month != null && param.month[0] == '5'}">May</option>
                            <option value="6" th:selected="${param.month != null && param.month[0] == '6'}">June</option>
                            <option value="7" th:selected="${param.month != null && param.month[0] == '7'}">July</option>
                            <option value="8" th:selected="${param.month != null && param.month[0] == '8'}">August</option>
                            <option value="9" th:selected="${param.month != null && param.month[0] == '9'}">September</option>
                            <option value="10" th:selected="${param.month != null && param.month[0] == '10'}">October</option>
                            <option value="11" th:selected="${param.month != null && param.month[0] == '11'}">November</option>
                            <option value="12" th:selected="${param.month != null && param.month[0] == '12'}">December</option>
                        </select>
                    </div>
                    <div class="col-md-5">
                        <label for="year" class="form-label">Year</label>
                        <input type="number" class="form-control" id="year" name="year" 
                               th:value="${param.year != null ? param.year[0] : currentYear}" 
                               min="2020" max="2030">
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">Search</button>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="mb-3">
            <a th:href="@{/expenses}" class="btn btn-outline-secondary">Clear Search</a>
        </div>
        
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>Month</th>
                    <th>Year</th>
                    <th>Number of Expenses</th>
                    <th>Total Amount</th>
                    <th>Last Modified</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <tr th:each="expense : ${expenses}">
                    <td th:text="${expense.month == 1 ? 'January' : 
                                  expense.month == 2 ? 'February' : 
                                  expense.month == 3 ? 'March' : 
                                  expense.month == 4 ? 'April' : 
                                  expense.month == 5 ? 'May' : 
                                  expense.month == 6 ? 'June' : 
                                  expense.month == 7 ? 'July' : 
                                  expense.month == 8 ? 'August' : 
                                  expense.month == 9 ? 'September' : 
                                  expense.month == 10 ? 'October' : 
                                  expense.month == 11 ? 'November' : 'December'}"></td>
                    <td th:text="${expense.year}"></td>
                    <td th:text="${expense.monthlyExpenses != null ? expense.monthlyExpenses.size() : 0}"></td>
                    <td th:text="${expense.monthlyExpenses != null ? #aggregates.sum(expense.monthlyExpenses.![amount]) : 0}"></td>
                    <td th:text="${#temporals.format(expense.modifiedAt, 'dd-MM-yyyy HH:mm')}"></td>
                    <td>
                        <a th:href="@{/expenses/{id}(id=${expense.id})}" class="btn btn-sm btn-info">View Details</a>
                    </td>
                </tr>
                <tr th:if="${#lists.isEmpty(expenses)}">
                    <td colspan="6" class="text-center">No expense records found</td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div th:replace="~{fragments/layout :: footer}"></div>
</body>
</html>