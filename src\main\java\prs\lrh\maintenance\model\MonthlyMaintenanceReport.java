package prs.lrh.maintenance.model;

import lombok.Data;
import java.util.List;

@Data
public class MonthlyMaintenanceReport {
    private int month;
    private int year;
    private List<MaintenanceItem> items;
    
    // Summary statistics
    private double totalBilled;
    private double totalPaid;
    private double totalOutstanding;
    
    // Constructor
    public MonthlyMaintenanceReport(int month, int year, List<MaintenanceItem> items) {
        this.month = month;
        this.year = year;
        this.items = items;
        
        // Calculate summary statistics
        this.totalBilled = items.stream()
            .filter(item -> item.getMonthlyMaintenanceRecord() != null)
            .mapToDouble(item -> item.getMonthlyMaintenanceRecord().getTotal())
            .sum();
            
        this.totalPaid = items.stream()
            .mapToDouble(MaintenanceItem::getTotalAmtPaid)
            .sum();
            
        this.totalOutstanding = this.totalBilled - this.totalPaid;
    }
}
