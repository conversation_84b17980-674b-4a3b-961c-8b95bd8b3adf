<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:replace="~{fragments/layout :: head('Add New Payment')}">
</head>
<body>
    <div th:replace="~{fragments/layout :: header}"></div>
    
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Add New Payment</h1>
            <a th:href="@{/payments}" class="btn btn-outline-secondary">Back to List</a>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Payment Details</h5>
            </div>
            <div class="card-body">
                <form th:action="@{/payments/add}" method="post">
                    <div class="mb-3">
                        <label for="houseId" class="form-label">House</label>
                        <select class="form-select" id="houseId" name="houseId" required>
                            <option value="">Select a house</option>
                            <option th:each="house : ${houses}" 
                                    th:value="${house.id}" 
                                    th:text="${house.houseNumber + ' - ' + house.ownerName}"
                                    th:selected="${selectedHouseId != null && selectedHouseId == house.id}"></option>
                        </select>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="month" class="form-label">Month</label>
                            <select class="form-select" id="month" name="month" required>
                                <option value="">Select Month</option>
                                <option value="1" th:selected="${#calendars.month(#calendars.createNow()) == 1}">January</option>
                                <option value="2" th:selected="${#calendars.month(#calendars.createNow()) == 2}">February</option>
                                <option value="3" th:selected="${#calendars.month(#calendars.createNow()) == 3}">March</option>
                                <option value="4" th:selected="${#calendars.month(#calendars.createNow()) == 4}">April</option>
                                <option value="5" th:selected="${#calendars.month(#calendars.createNow()) == 5}">May</option>
                                <option value="6" th:selected="${#calendars.month(#calendars.createNow()) == 6}">June</option>
                                <option value="7" th:selected="${#calendars.month(#calendars.createNow()) == 7}">July</option>
                                <option value="8" th:selected="${#calendars.month(#calendars.createNow()) == 8}">August</option>
                                <option value="9" th:selected="${#calendars.month(#calendars.createNow()) == 9}">September</option>
                                <option value="10" th:selected="${#calendars.month(#calendars.createNow()) == 10}">October</option>
                                <option value="11" th:selected="${#calendars.month(#calendars.createNow()) == 11}">November</option>
                                <option value="12" th:selected="${#calendars.month(#calendars.createNow()) == 12}">December</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="year" class="form-label">Year</label>
                            <input type="number" class="form-control" id="year" name="year" 
                                   th:value="${#calendars.format(#calendars.createNow(), 'yyyy')}" 
                                   min="2020" max="2030" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="amtPayable" class="form-label">Amount Payable</label>
                        <input type="text" class="form-control" id="amtPayable" name="amtPayable" 
                               th:value="${defaultAmtPayable != null ? #numbers.formatDecimal(defaultAmtPayable, 1, 2) : ''}" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="amtPaid" class="form-label">Amount Paid</label>
                        <input type="text" class="form-control" id="amtPaid" name="amtPaid" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="comments" class="form-label">Comments</label>
                        <textarea class="form-control" id="comments" name="comments" rows="3"></textarea>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">Save Payment</button>
                </form>
            </div>
        </div>
    </div>
    
    <div th:replace="~{fragments/layout :: footer}"></div>
</body>
</html>

