<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:replace="~{fragments/layout :: head('Maintenance Record Details')}">
</head>
<body>
    <div th:replace="~{fragments/layout :: header}"></div>
    
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Maintenance Record Details</h1>
            <a th:href="@{/maintenance}" class="btn btn-secondary">Back to List</a>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    Maintenance for 
                    <span th:text="${maintenanceRecord.month == 1 ? 'January' : 
                                    maintenanceRecord.month == 2 ? 'February' : 
                                    maintenanceRecord.month == 3 ? 'March' : 
                                    maintenanceRecord.month == 4 ? 'April' : 
                                    maintenanceRecord.month == 5 ? 'May' : 
                                    maintenanceRecord.month == 6 ? 'June' : 
                                    maintenanceRecord.month == 7 ? 'July' : 
                                    maintenanceRecord.month == 8 ? 'August' : 
                                    maintenanceRecord.month == 9 ? 'September' : 
                                    maintenanceRecord.month == 10 ? 'October' : 
                                    maintenanceRecord.month == 11 ? 'November' : 'December'}"></span>
                    <span th:text="${maintenanceRecord.year}"></span>
                </h5>
            </div>
        </div>
        
        <h2>Maintenance Records</h2>
        <form th:action="@{/maintenance/update}" method="post">
            <input type="hidden" name="maintenanceId" th:value="${maintenanceRecord.id}">
            <input type="hidden" name="month" th:value="${maintenanceRecord.month}">
            <input type="hidden" name="year" th:value="${maintenanceRecord.year}">
            
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th data-bs-toggle="tooltip" data-bs-title="House Number">House Number</th>
                        <th data-bs-toggle="tooltip" data-bs-title="Fixed Amount">Fixed Amount</th>
                        <th data-bs-toggle="tooltip" data-bs-title="Water Bill">Water Bill</th>
                        <th data-bs-toggle="tooltip" data-bs-title="Garbage Collection Fee">Garbage</th>
                        <th data-bs-toggle="tooltip" data-bs-title="Other Charges">Other Charges</th>
                        <th data-bs-toggle="tooltip" data-bs-title="Past Dues">Past Dues</th>
                        <th data-bs-toggle="tooltip" data-bs-title="Total Amount">Total</th>
                        <th data-bs-toggle="tooltip" data-bs-title="Additional Comments">Comments</th>
                        <th data-bs-toggle="tooltip" data-bs-title="Save Individual Record">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr th:each="record, stat : ${maintenanceRecord.monthlyMaintenanceRecords}">
                        <input type="hidden" th:name="'records[' + ${stat.index} + '].id'" th:value="${record.id}">
                        <input type="hidden" th:name="'records[' + ${stat.index} + '].houseId'" th:value="${record.houseId}">
                        <input type="hidden" th:name="'records[' + ${stat.index} + '].houseNumber'" th:value="${record.houseNumber}">
                        <input type="hidden" th:name="'records[' + ${stat.index} + '].createdAt'" th:value="${record.createdAt}">
                        
                        <td th:text="${record.houseNumber}"></td>
                        <td>
                            <input type="number" step="0.01" class="form-control"
                                   th:name="'records[' + ${stat.index} + '].fixedAmt'"
                                   th:value="${record.fixedAmt}"
                                   th:readonly="${!isSuperUser}"
                                   data-bs-toggle="tooltip" data-bs-title="Fixed monthly maintenance amount"
                                   onchange="calculateTotal(this)">
                        </td>
                        <td>
                            <input type="number" step="0.01" class="form-control" 
                                   th:name="'records[' + ${stat.index} + '].waterBill'" 
                                   th:value="${record.waterBill}"
                                   data-bs-toggle="tooltip" data-bs-title="Water consumption charges"
                                   onchange="calculateTotal(this)">
                        </td>
                        <td>
                            <input type="number" step="0.01" class="form-control" 
                                   th:name="'records[' + ${stat.index} + '].garbage'" 
                                   th:value="${record.garbage}"
                                   data-bs-toggle="tooltip" data-bs-title="Garbage collection fee"
                                   th:readonly="${!isSuperUser}"
                                   onchange="calculateTotal(this)">
                        </td>
                        <td>
                            <input type="number" step="0.01" class="form-control" 
                                   th:name="'records[' + ${stat.index} + '].otherCharges'" 
                                   th:value="${record.otherCharges}"
                                   data-bs-toggle="tooltip" data-bs-title="Additional charges"
                                   onchange="calculateTotal(this)">
                        </td>
                        <td>
                            <input type="number" step="0.01" class="form-control" 
                                   th:name="'records[' + ${stat.index} + '].pastDues'" 
                                   th:value="${record.pastDues}"
                                   data-bs-toggle="tooltip" data-bs-title="Previous unpaid balance"
                                   th:readonly="${!isSuperUser}"
                                   onchange="calculateTotal(this)">
                        </td>
                        <td>
                            <input type="number" step="0.01" class="form-control" 
                                   th:name="'records[' + ${stat.index} + '].total'" 
                                   th:value="${record.total}" 
                                   data-bs-toggle="tooltip" data-bs-title="Sum of all charges"
                                   readonly>
                        </td>
                        <td>
                            <input type="text" class="form-control" 
                                   th:name="'records[' + ${stat.index} + '].comments'" 
                                   th:value="${record.comments}"
                                   data-bs-toggle="tooltip" data-bs-title="Additional notes">
                        </td>
                        <td>
                            <button type="button" class="btn btn-sm btn-success" 
                                    data-bs-toggle="tooltip" data-bs-title="Save this record individually"
                                    th:onclick="'saveRecord(\'' + ${record.id} + '\', ' + ${stat.index} + ')'">Save</button>
                        </td>
                    </tr>
                    <tr th:if="${#lists.isEmpty(maintenanceRecord.monthlyMaintenanceRecords)}">
                        <td colspan="8" class="text-center">No maintenance records found</td>
                    </tr>
                </tbody>
            </table>
            
            <div class="d-flex justify-content-end mt-3">
                <button type="submit" class="btn btn-primary">Save Changes</button>
            </div>
        </form>
    </div>
    
    <div th:replace="~{fragments/layout :: footer}"></div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Initialize tooltips
        document.addEventListener('DOMContentLoaded', function() {
            const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
            const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));
        });
        
        function calculateTotal(input) {
            const row = input.closest('tr');
            const fixedAmt = parseFloat(row.querySelector('input[name$=".fixedAmt"]').value) || 0;
            const waterBill = parseFloat(row.querySelector('input[name$=".waterBill"]').value) || 0;
            const garbage = parseFloat(row.querySelector('input[name$=".garbage"]').value) || 0;
            const otherCharges = parseFloat(row.querySelector('input[name$=".otherCharges"]').value) || 0;
            const pastDues = parseFloat(row.querySelector('input[name$=".pastDues"]').value) || 0;
            
            const total = fixedAmt + waterBill + garbage + otherCharges + pastDues;
            row.querySelector('input[name$=".total"]').value = total.toFixed(2);
        }
        
        function saveRecord(recordId, index) {
            // Get values from the row
            const form = document.getElementById('recordForm');
            const maintenanceId = document.querySelector('input[name="maintenanceId"]').value;
            const month = document.querySelector('input[name="month"]').value;
            const year = document.querySelector('input[name="year"]').value;
            
            // Set values in the hidden form
            document.getElementById('singleRecordId').value = recordId;
            document.getElementById('singleMaintenanceId').value = maintenanceId;
            document.getElementById('singleMonth').value = month;
            document.getElementById('singleYear').value = year;
            
            // Copy values from the main form
            const row = document.querySelector('tr:nth-child(' + (index + 1) + ')');
            document.getElementById('singleHouseId').value = row.querySelector('input[name$=".houseId"]').value;
            document.getElementById('singleHouseNumber').value = row.querySelector('input[name$=".houseNumber"]').value;
            document.getElementById('singleFixedAmt').value = row.querySelector('input[name$=".fixedAmt"]').value;
            document.getElementById('singleWaterBill').value = row.querySelector('input[name$=".waterBill"]').value;
            document.getElementById('singleGarbage').value = row.querySelector('input[name$=".garbage"]').value;
            document.getElementById('singleOtherCharges').value = row.querySelector('input[name$=".otherCharges"]').value;
            document.getElementById('singlePastDues').value = row.querySelector('input[name$=".pastDues"]').value;
            document.getElementById('singleTotal').value = row.querySelector('input[name$=".total"]').value;
            document.getElementById('singleComments').value = row.querySelector('input[name$=".comments"]').value;
            
            // Submit the form
            document.getElementById('singleRecordForm').submit();
        }
    </script>
    
    <!-- Hidden form for saving individual records -->
    <form id="singleRecordForm" th:action="@{/maintenance/update-record}" method="post" style="display: none;">
        <input type="hidden" id="singleRecordId" name="recordId">
        <input type="hidden" id="singleMaintenanceId" name="maintenanceId">
        <input type="hidden" id="singleMonth" name="month">
        <input type="hidden" id="singleYear" name="year">
        <input type="hidden" id="singleHouseId" name="houseId">
        <input type="hidden" id="singleHouseNumber" name="houseNumber">
        <input type="hidden" id="singleFixedAmt" name="fixedAmt">
        <input type="hidden" id="singleWaterBill" name="waterBill">
        <input type="hidden" id="singleGarbage" name="garbage">
        <input type="hidden" id="singleOtherCharges" name="otherCharges">
        <input type="hidden" id="singlePastDues" name="pastDues">
        <input type="hidden" id="singleTotal" name="total">
        <input type="hidden" id="singleComments" name="comments">
    </form>
</body>
</html>





