package prs.lrh.maintenance.config;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.client.authentication.OAuth2AuthenticationToken;
import org.springframework.security.oauth2.core.user.DefaultOAuth2User;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.security.web.authentication.SavedRequestAwareAuthenticationSuccessHandler;
import org.springframework.stereotype.Component;
import prs.lrh.maintenance.model.User;
import prs.lrh.maintenance.service.UserService;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Component
public class CustomAuthenticationSuccessHandler extends SavedRequestAwareAuthenticationSuccessHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(CustomAuthenticationSuccessHandler.class);
    
    @Autowired
    private UserService userService;
    
    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response, 
                                        Authentication authentication) throws IOException, ServletException {
        
        if (authentication instanceof OAuth2AuthenticationToken) {
            OAuth2AuthenticationToken oauthToken = (OAuth2AuthenticationToken) authentication;
            OAuth2User oAuth2User = oauthToken.getPrincipal();
            
            logger.info("User successfully authenticated with OAuth2");
            logger.info("Authentication provider: {}", oauthToken.getAuthorizedClientRegistrationId());
            logger.info("User name: {}", oAuth2User.getName());
            
            Map<String, Object> attributes = oAuth2User.getAttributes();
            logger.info("User attributes: {}", attributes);
            
            if (attributes.containsKey("email")) {
                String email = (String) attributes.get("email");
                logger.info("User email: {}", email);
                
                // Check if user exists in the database
                Optional<User> userOpt = userService.getUserByEmail(email);
                
                if (userOpt.isPresent()) {
                    User user = userOpt.get();

                    // Check if user is active
                    if (!user.isActive()) {
                        logger.warn("User account is not active: {}", email);
                        throw new DisabledException("User account is not active");
                    }

                    // Add user role to authentication attributes
                    Map<String, Object> modifiableAttributes = new HashMap<>(attributes);
                    modifiableAttributes.put("role", user.getRole().name());
                    modifiableAttributes.put("userId", user.getId());

                    // Create a new OAuth2User with the updated attributes
                    OAuth2User updatedOAuth2User = new DefaultOAuth2User(
                        oAuth2User.getAuthorities(),
                        modifiableAttributes,
                        "email" // nameAttributeKey
                    );

                    // Create a new authentication token with the updated user
                    OAuth2AuthenticationToken updatedToken = new OAuth2AuthenticationToken(
                        updatedOAuth2User,
                        oauthToken.getAuthorities(),
                        oauthToken.getAuthorizedClientRegistrationId()
                    );

                    // Set the updated authentication in the security context
                    SecurityContextHolder.getContext().setAuthentication(updatedToken);

                    // Update last login date
                    userService.updateLastLoginDate(email);
                    logger.info("Updated last login date for user: {} with role: {}", email, user.getRole());
                } else {
                    logger.warn("User not found in database: {}", email);
                    response.sendRedirect("/access-denied");
                    return;
                }
            }
            
            if (attributes.containsKey("name")) {
                logger.info("User full name: {}", attributes.get("name"));
            }
            
            if (attributes.containsKey("picture")) {
                logger.info("User picture URL: {}", attributes.get("picture"));
            }
        } else {
            logger.info("User authenticated with non-OAuth2 method: {}", 
                      authentication.getClass().getSimpleName());
        }
        
        super.onAuthenticationSuccess(request, response, authentication);
    }
}
