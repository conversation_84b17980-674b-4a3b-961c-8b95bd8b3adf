package prs.lrh.maintenance.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import prs.lrh.maintenance.model.HouseRecord;
import prs.lrh.maintenance.repository.HouseRepository;

import java.util.List;
import java.util.Comparator;
import java.util.stream.Collectors;
import java.util.ArrayList;

@Controller
@RequestMapping("/houses")
public class HouseController {

    @Autowired
    private HouseRepository houseRepository;

    @GetMapping
    public String listHouses(Model model, 
                            @RequestParam(required = false) String houseNumber,
                            @RequestParam(required = false) String searchType,
                            @RequestParam(required = false) String searchTerm) {
        
        List<HouseRecord> houses;
        
        // Search by exact house number if provided
        if (houseNumber != null && !houseNumber.isEmpty()) {
            HouseRecord house = houseRepository.findByHouseNumber(houseNumber);
            if (house != null) {
                houses = List.of(house);
            } else {
                houses = List.of();
            }
        } 
        // Search by search type and term if provided
        else if (searchType != null && searchTerm != null && !searchTerm.isEmpty()) {
            switch (searchType) {
                case "houseNumber":
                    houses = houseRepository.findByHouseNumberContainingIgnoreCase(searchTerm);
                    break;
                case "ownerName":
                    houses = houseRepository.findByOwnerNameContainingIgnoreCase(searchTerm);
                    break;
                case "residentName":
                    houses = houseRepository.findByResidentNameContainingIgnoreCase(searchTerm);
                    break;
                default:
                    houses = houseRepository.findAllByOrderByIdAsc();
            }
        } 
        // Otherwise, return all houses sorted by ID
        else {
            houses = houseRepository.findAllByOrderByIdAsc();
        }
        
        // Sort houses by ID if they're not already sorted
        if (!houses.isEmpty() && (searchType != null || houseNumber != null)) {
            houses = houses.stream()
                .sorted(Comparator.comparing(HouseRecord::getId))
                .collect(Collectors.toList());
        }
        
        // Chunk the houses list into groups of 10
        List<List<HouseRecord>> groupedHouses = new ArrayList<>();
        for (int i = 0; i < houses.size(); i += 10) {
            groupedHouses.add(houses.subList(i, Math.min(i + 10, houses.size())));
        }
        
        model.addAttribute("houses", houses);
        model.addAttribute("groupedHouses", groupedHouses);
        return "houses";
    }
}








