<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:replace="~{fragments/layout :: head('Account Balances')}">
</head>
<body>
    <div th:replace="~{fragments/layout :: header}"></div>
    
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Account Balances</h1>
            <div th:if="${showCreateButton}">
                <a th:href="@{/account-balances/generate-current}" 
                   class="btn btn-success">
                    Generate Current Month Account Balance
                </a>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">Search Account Balances</div>
            <div class="card-body">
                <form th:action="@{/account-balances}" method="get" class="row g-3">
                    <div class="col-md-10">
                        <label for="year" class="form-label">Year</label>
                        <input type="number" class="form-control" id="year" name="year" 
                               th:value="${param.year != null ? param.year[0] : currentYear}" 
                               min="2020" max="2030">
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">Search</button>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="mb-3 d-flex justify-content-between">
            <a th:href="@{/account-balances}" class="btn btn-outline-secondary">Clear Search</a>
            <button id="exportPdfBtn" class="btn btn-secondary">Export as PDF</button>
            <a th:href="@{/account-balances/pdf(year=${currentYear})}" 
               class="btn btn-secondary ml-2">Export as PDF (Server-side)</a>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">Summary</div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h5 class="card-title">Current Balance</h5>
                                <p class="card-text fs-4" th:text="${'Rs.' + #numbers.formatDecimal(currentBalance, 1, 'COMMA', 2, 'POINT')}"></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h5 class="card-title">Total Payments</h5>
                                <p class="card-text fs-4" th:text="${'Rs.' + #numbers.formatDecimal(totalPayments, 1, 'COMMA', 2, 'POINT')}"></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h5 class="card-title">Total Credits</h5>
                                <p class="card-text fs-4" th:text="${'Rs.' + #numbers.formatDecimal(totalCredits, 1, 'COMMA', 2, 'POINT')}"></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center">
                                <h5 class="card-title">Total Expenses</h5>
                                <p class="card-text fs-4" th:text="${'Rs.' + #numbers.formatDecimal(totalExpenses, 1, 'COMMA', 2, 'POINT')}"></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>Month</th>
                    <th>Year</th>
                    <th>Previous Balance</th>
                    <th>Payments</th>
                    <th>Credits</th>
                    <th>Expenses</th>
                    <th>Current Balance</th>
                    <th>Last Updated</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <tr th:each="balance : ${accountBalances}">
                    <td th:text="${balance.month == 1 ? 'January' : 
                                  balance.month == 2 ? 'February' : 
                                  balance.month == 3 ? 'March' : 
                                  balance.month == 4 ? 'April' : 
                                  balance.month == 5 ? 'May' : 
                                  balance.month == 6 ? 'June' : 
                                  balance.month == 7 ? 'July' : 
                                  balance.month == 8 ? 'August' : 
                                  balance.month == 9 ? 'September' : 
                                  balance.month == 10 ? 'October' : 
                                  balance.month == 11 ? 'November' : 'December'}"></td>
                    <td th:text="${balance.year}"></td>
                    <td th:text="${'Rs.' + #numbers.formatDecimal(balance.previousMonthBalance, 1, 'COMMA', 2, 'POINT')}"></td>
                    <td th:text="${'Rs.' + #numbers.formatDecimal(balance.payments, 1, 'COMMA', 2, 'POINT')}"></td>
                    <td th:text="${'Rs.' + #numbers.formatDecimal(balance.credits, 1, 'COMMA', 2, 'POINT')}"></td>
                    <td th:text="${'Rs.' + #numbers.formatDecimal(balance.expenses, 1, 'COMMA', 2, 'POINT')}"></td>
                    <td th:text="${'Rs.' + #numbers.formatDecimal(balance.currentBalance, 1, 'COMMA', 2, 'POINT')}"></td>
                    <td th:text="${#temporals.format(balance.updatedOn, 'dd-MM-yyyy HH:mm')}"></td>
                    <td>
                        <a th:href="@{/account-balances/{id}(id=${balance.id})}" class="btn btn-sm btn-info">View</a>
                        <a th:if="${balance.recalculate}" 
                           th:href="@{/account-balances/recalculate/{id}(id=${balance.id})}" 
                           class="btn btn-sm btn-warning"
                           onclick="return confirm('Are you sure you want to recalculate this balance?')">
                            Recalculate
                        </a>
                    </td>
                </tr>
                <tr th:if="${#lists.isEmpty(accountBalances)}">
                    <td colspan="9" class="text-center">No account balance records found</td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div th:replace="~{fragments/layout :: footer}"></div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Add any JavaScript functionality here
        });
    </script>
</body>
</html>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // PDF Export functionality
            document.getElementById('exportPdfBtn').addEventListener('click', function() {
                // Create PDF document
                const { jsPDF } = window.jspdf;
                const doc = new jsPDF();
                
                // Get page dimensions
                const pageWidth = doc.internal.pageSize.getWidth();
                
                // Add title
                doc.setFontSize(16);
                doc.text('Account Balances Report', 14, 20);
                
                // Add generation date
                const now = new Date();
                const dateStr = now.toLocaleDateString();
                doc.setFontSize(10);
                doc.text(`Generated on: ${dateStr}`, 14, 30);
                
                // Add summary data
                const currentBalance = document.querySelector('.col-md-3:nth-child(1) .card-text').textContent;
                const totalPayments = document.querySelector('.col-md-3:nth-child(2) .card-text').textContent;
                const totalCredits = document.querySelector('.col-md-3:nth-child(3) .card-text').textContent;
                const totalExpenses = document.querySelector('.col-md-3:nth-child(4) .card-text').textContent;
                
                doc.setFontSize(12);
                doc.text(`Current Balance: ${currentBalance}`, 14, 45);
                doc.text(`Total Payments: ${totalPayments}`, 14, 52);
                doc.text(`Total Credits: ${totalCredits}`, 14, 59);
                doc.text(`Total Expenses: ${totalExpenses}`, 14, 66);
                
                // Create table data from the HTML table
                const table = document.querySelector('.table');
                
                // We only want Month, Year, Payments, Credits, and Current Balance columns
                const headers = ['Month', 'Year', 'Payments', 'Credits', 'Current Balance'];
                
                const rows = Array.from(table.querySelectorAll('tbody tr:not([th\\:if])')).map(tr => {
                    const cells = Array.from(tr.querySelectorAll('td'));
                    // Extract only the columns we want (Month, Year, Payments, Credits, Current Balance)
                    return [
                        cells[0].textContent.trim(), // Month
                        cells[1].textContent.trim(), // Year
                        cells[3].textContent.trim(), // Payments
                        cells[4].textContent.trim(), // Credits
                        cells[6].textContent.trim()  // Current Balance
                    ];
                });
                
                // Generate the table in the PDF
                doc.autoTable({
                    head: [headers],
                    body: rows,
                    startY: 75,
                    theme: 'grid',
                    styles: {
                        fontSize: 8,
                        cellPadding: 2
                    },
                    headStyles: {
                        fillColor: [66, 139, 202],
                        textColor: 255
                    },
                    alternateRowStyles: {
                        fillColor: [240, 240, 240]
                    }
                });
                
                // Add footer with page numbers
                const pageCount = doc.internal.getNumberOfPages();
                
                for (let i = 1; i <= pageCount; i++) {
                    doc.setPage(i);
                    doc.setFontSize(8);
                    doc.text(`Page ${i} of ${pageCount}`, doc.internal.pageSize.width - 30, doc.internal.pageSize.height - 10);
                }
                
                // Save the PDF
                doc.save(`Account_Balances_Report_${dateStr.replace(/\//g, '-')}.pdf`);
            });
        });
    </script>
</body>
</html>


