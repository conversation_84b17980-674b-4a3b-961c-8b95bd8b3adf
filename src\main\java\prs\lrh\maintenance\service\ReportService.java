package prs.lrh.maintenance.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import prs.lrh.maintenance.model.*;
import prs.lrh.maintenance.repository.HouseRepository;
import prs.lrh.maintenance.repository.MonthlyMaintenanceRepository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ReportService {

    @Autowired
    private HouseRepository houseRepository;
    
    @Autowired
    private MonthlyMaintenanceRepository monthlyMaintenanceRepository;
    
    @Autowired
    private MongoDBService mongoDBService;
    
    /**
     * Generate a monthly maintenance report for the specified month and year
     */
    public MonthlyMaintenanceReport generateMonthlyReport(int month, int year) {
        // Get all houses sorted by ID
        List<HouseRecord> houses = houseRepository.findAllByOrderByIdAsc();
        
        // Get maintenance records for the month/year
        List<MonthlyMaintenance> maintenanceRecords = monthlyMaintenanceRepository.findByMonthAndYear(month, year);
        
        // Create a map of house ID to maintenance record for quick lookup
        final Map<Long, MonthlyMaintenanceRecord> maintenanceMap = !maintenanceRecords.isEmpty() ?
            maintenanceRecords.get(0).getMonthlyMaintenanceRecords().stream()
                .collect(Collectors.toMap(MonthlyMaintenanceRecord::getHouseId, record -> record)) :
            new HashMap<>();
        
        
        
        
        
        // Create maintenance items for each house
        List<MaintenanceItem> items = houses.stream()
            .map(house -> {
                MonthlyMaintenanceRecord maintenanceRecord = maintenanceMap.get(house.getId());
                // Get House specific payment records for specified month/year
                List<Payment> payments = mongoDBService.findPaymentsByHouseIdMonthAndYear(house.getId(), month, year);
                
                return new MaintenanceItem(house, maintenanceRecord, payments);
            })
            .collect(Collectors.toList());
        
        // Create and return the report
        return new MonthlyMaintenanceReport(month, year, items);
    }
}






