package prs.lrh.maintenance.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ResponseBody;
import prs.lrh.maintenance.model.MaintenanceItem;
import prs.lrh.maintenance.model.MonthlyMaintenanceReport;
import prs.lrh.maintenance.service.ReportService;

import java.time.YearMonth;
import java.time.LocalDateTime;
import java.time.Month;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.Locale;
import com.lowagie.text.Document;
import com.lowagie.text.DocumentException;
import com.lowagie.text.Element;
import com.lowagie.text.Font;
import com.lowagie.text.FontFactory;
import com.lowagie.text.Paragraph;
import com.lowagie.text.Phrase;
import com.lowagie.text.pdf.PdfPCell;
import com.lowagie.text.pdf.PdfPTable;
import com.lowagie.text.pdf.PdfWriter;
import java.io.ByteArrayOutputStream;
import com.lowagie.text.Image;
import com.lowagie.text.pdf.PdfContentByte;
import com.lowagie.text.pdf.PdfPageEventHelper;
import org.springframework.core.io.ClassPathResource;

@Controller
@RequestMapping("/reports")
public class ReportController {

    private static final Logger logger = LoggerFactory.getLogger(ReportController.class);
    private static final String INR_SYMBOL = "Rs.";
    
    @Autowired
    private ReportService reportService;
    
    @GetMapping("/monthly")
    public String generateMonthlyReport(Model model,
                                       @RequestParam(required = false) Integer month,
                                       @RequestParam(required = false) Integer year) {
        
        // If month or year not provided, use current month/year
        if (month == null || year == null) {
            YearMonth currentYearMonth = YearMonth.now();
            month = currentYearMonth.getMonthValue();
            year = currentYearMonth.getYear();
        }
        
        logger.info("Generating monthly report for {}/{}", month, year);
        
        // Generate the report
        MonthlyMaintenanceReport report = reportService.generateMonthlyReport(month, year);
        
        // Add to model
        model.addAttribute("report", report);
        model.addAttribute("currentMonth", month);
        model.addAttribute("currentYear", year);
        
        return "monthly-report";
    }

    @GetMapping("/monthly/pdf")
    @ResponseBody
    public ResponseEntity<byte[]> generateMonthlyReportPdf(
            @RequestParam(required = false) Integer month,
            @RequestParam(required = false) Integer year) throws DocumentException {
        
        // If month or year not provided, use current month/year
        if (month == null || year == null) {
            YearMonth currentYearMonth = YearMonth.now();
            month = currentYearMonth.getMonthValue();
            year = currentYearMonth.getYear();
        }
        
        logger.info("Generating monthly report PDF for {}/{}", month, year);
        
        // Generate the report
        MonthlyMaintenanceReport report = reportService.generateMonthlyReport(month, year);
        
        // Create PDF document
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        Document document = new Document();
        PdfWriter writer = PdfWriter.getInstance(document, baos);

        // Add page event handler for the logo
        writer.setPageEvent(new PdfPageEventHelper() {
            public void onEndPage(PdfWriter writer, Document document) {
                try {
                    // Load the logo image
                    Image logo = Image.getInstance(new ClassPathResource("static/images/lake_ridge_homes_header.jpg").getURL());
                    
                    // Scale the logo to fit about 50% of page width
                    float pageWidth = document.getPageSize().getWidth();
                    float logoWidth = pageWidth * 0.5f;
                    logo.scaleToFit(logoWidth, 60); // Maintain aspect ratio with max height 50
                    
                    // Position the logo at the top left corner of the page
                    // Add small margins (20 points from left, 20 points from top)                    
                    logo.setAbsolutePosition(35, document.getPageSize().getHeight() - 60);
                    
                    // Add the logo to the page
                    PdfContentByte cb = writer.getDirectContent();
                    cb.addImage(logo);
                } catch (Exception e) {
                    logger.error("Error adding logo to PDF: {}", e.getMessage());
                }
            }
        });

        // Adjust top margin to make room for the logo
        document.setMargins(document.leftMargin(), document.rightMargin(), 95, document.bottomMargin());
        
        document.open();
        
        // Add title
        Font titleFont = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 18);
        Paragraph title = new Paragraph("Monthly Maintenance Report", titleFont);
        title.setAlignment(Element.ALIGN_LEFT);
        document.add(title);
        
        // Add month and year
        String monthName = Month.of(month).getDisplayName(TextStyle.FULL, Locale.ENGLISH);
        Font subtitleFont = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 14);
        Paragraph subtitle = new Paragraph(monthName + " " + year, subtitleFont);
        subtitle.setAlignment(Element.ALIGN_LEFT);
        subtitle.setSpacingBefore(10);
        subtitle.setSpacingAfter(20);
        document.add(subtitle);
        
        // Add summary
        Font summaryFont = FontFactory.getFont(FontFactory.HELVETICA, 12);
        Paragraph summary = new Paragraph();
        summary.setFont(summaryFont);
        //summary.add("Total Billed: "+ INR_SYMBOL + String.format("%,14.2f", report.getTotalBilled()) + "\n");
        summary.add("Total Paid: "+ INR_SYMBOL + String.format("%,12.2f", report.getTotalPaid()) + "\n");
        //summary.add("Outstanding: "+ INR_SYMBOL + String.format("%,14.2f", report.getTotalOutstanding()) + "\n");
        summary.setSpacingAfter(20);
        document.add(summary);
        
        // Create a table for each maintenance item
        for (MaintenanceItem item : report.getItems()) {
            // Get owner/resident name
            String name = item.getHouseRecord().getOwnerName();
            if(item.getHouseRecord().getResidentName() != null && item.getHouseRecord().getResidentName().length() > 0 &&
                !item.getHouseRecord().getOwnerName().equalsIgnoreCase(item.getHouseRecord().getResidentName())) {
                name += "/" + item.getHouseRecord().getResidentName(); 
            }
            
            // Get month-year string
            String period = monthName + "-" + year;
            
            // Create item table with the exact layout from template
            PdfPTable itemTable = new PdfPTable(7);
            itemTable.setWidthPercentage(100);
            float[] columnWidths = {1.5f, 1.5f, 1.5f, 1.5f, 0.8f, 0.8f, 0.8f};
            itemTable.setWidths(columnWidths);
            
            Font headerFont = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 8);
            Font dataFont = FontFactory.getFont(FontFactory.HELVETICA, 8);
            
            // Row 1: House No, Owner/Resident, Period
            PdfPCell cell = new PdfPCell(new Phrase("House No: " + item.getHouseRecord().getHouseNumber(), headerFont));
            cell.setColspan(2);
            itemTable.addCell(cell);
            
            cell = new PdfPCell(new Phrase("Owner/Resident Name: " + name, headerFont));
            cell.setColspan(3);
            itemTable.addCell(cell);
            
            cell = new PdfPCell(new Phrase("Period: " + period, headerFont));
            cell.setColspan(2);
            itemTable.addCell(cell);
            
            // Row 2: Fixed Amt, Water Bill, Past Dues
            cell = new PdfPCell(new Phrase("Fixed Amt: " + (item.getMonthlyMaintenanceRecord() != null ? 
                    INR_SYMBOL + String.format("%,.2f", item.getMonthlyMaintenanceRecord().getFixedAmt()) : "-"), dataFont));
            cell.setColspan(2);
            itemTable.addCell(cell);
            
            cell = new PdfPCell(new Phrase("Water Bill: " + (item.getMonthlyMaintenanceRecord() != null ? 
                    INR_SYMBOL + String.format("%,.2f", item.getMonthlyMaintenanceRecord().getWaterBill()) : "-"), dataFont));
            cell.setColspan(2);
            itemTable.addCell(cell);
            
            cell = new PdfPCell(new Phrase("Past Dues: " + (item.getMonthlyMaintenanceRecord() != null ? 
                    INR_SYMBOL + String.format("%,.2f", item.getMonthlyMaintenanceRecord().getPastDues()) : "-"), dataFont));
            cell.setColspan(3);
            itemTable.addCell(cell);
            
            // Row 3: Garbage Amt, Other Expenses, Comments
            cell = new PdfPCell(new Phrase("Garbage Expenses: " + (item.getMonthlyMaintenanceRecord() != null ? 
            INR_SYMBOL + String.format("%,.2f", item.getMonthlyMaintenanceRecord().getGarbage()) : "-"), dataFont));
            cell.setColspan(2);
            itemTable.addCell(cell);
            
            cell = new PdfPCell(new Phrase("Other Expenses: " + (item.getMonthlyMaintenanceRecord() != null ? 
                    INR_SYMBOL + String.format("%,.2f", item.getMonthlyMaintenanceRecord().getOtherCharges()) : "-"), dataFont));
            cell.setColspan(2);
            itemTable.addCell(cell);
            
            cell = new PdfPCell(new Phrase("Comments: " + (item.getMonthlyMaintenanceRecord() != null && 
                    item.getMonthlyMaintenanceRecord().getComments() != null ? 
                    item.getMonthlyMaintenanceRecord().getComments() : ""), dataFont));
            cell.setColspan(3);
            itemTable.addCell(cell);
            
            // Row 4: Total Amt, Amount Paid, Balance
            cell = new PdfPCell(new Phrase("Total Amt: " + (item.getMonthlyMaintenanceRecord() != null ? 
                    INR_SYMBOL + String.format("%,.2f", item.getMonthlyMaintenanceRecord().getTotal()) : "-"), headerFont));
            cell.setColspan(2);
            itemTable.addCell(cell);
            
            cell = new PdfPCell(new Phrase("Amount Paid: " + (item.getPayments() != null && !item.getPayments().isEmpty() ? 
                    INR_SYMBOL + String.format("%,.2f", item.getTotalAmtPaid()) : "-"), headerFont));
            cell.setColspan(3);
            itemTable.addCell(cell);
            
            cell = new PdfPCell(new Phrase("Balance:" + (item.getMonthlyMaintenanceRecord() != null ? 
                    INR_SYMBOL + String.format("%,.2f", item.getMonthlyMaintenanceRecord().getTotal() - item.getTotalAmtPaid()) : "-"), headerFont));
            cell.setColspan(2);
            itemTable.addCell(cell);
            
            // Add table to document with spacing
            itemTable.setSpacingAfter(15);
            document.add(itemTable);
        }
        document.close();
        
        // Prepare response
        byte[] pdfBytes = baos.toByteArray();
        
        HttpHeaders httpsHeaders = new HttpHeaders();
        httpsHeaders.setContentType(MediaType.APPLICATION_PDF);
        httpsHeaders.setContentDispositionFormData("attachment", 
                "Maintenance_Report_"+ LocalDateTime.now().format(DateTimeFormatter.ofPattern("dd-MM-yyyy_HH-mm")) +".pdf");
                httpsHeaders.setCacheControl("must-revalidate, post-check=0, pre-check=0");
        
        return ResponseEntity
                .ok()
                .headers(httpsHeaders)
                .contentLength(pdfBytes.length)
                .body(pdfBytes);
    }
}

