<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head th:replace="~{fragments/layout :: head('Monthly Readings')}"></head>
<body>
    <div th:replace="~{fragments/layout :: header}"></div>
    <div class="container mt-5">
        <h1>Monthly Readings</h1>
        
        <div class="card mb-4">
            <div class="card-header">Search Readings</div>
            <div class="card-body">
                <form th:action="@{/readings}" method="get" class="row g-3">
                    <div class="col-md-4">
                        <label for="month" class="form-label">Month</label>
                        <select class="form-select" id="month" name="month">
                            <option value="">All Months</option>
                            <option value="1">January</option>
                            <option value="2">February</option>
                            <option value="3">March</option>
                            <option value="4">April</option>
                            <option value="5">May</option>
                            <option value="6">June</option>
                            <option value="7">July</option>
                            <option value="8">August</option>
                            <option value="9">September</option>
                            <option value="10">October</option>
                            <option value="11">November</option>
                            <option value="12">December</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="year" class="form-label">Year</label>
                        <input type="number" class="form-control" id="year" name="year" placeholder="Enter year">
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">Search</button>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="mb-3">
            <a th:href="@{/readings}" class="btn btn-outline-secondary">Clear Search</a>
        </div>
        
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>Month</th>
                    <th>Year</th>
                    <th>Created At</th>
                    <th>Modified At</th>
                    <th>Number of Readings</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <tr th:each="reading : ${readings}">
                    <td th:text="${reading.month == 1 ? 'January' : 
                                  reading.month == 2 ? 'February' : 
                                  reading.month == 3 ? 'March' : 
                                  reading.month == 4 ? 'April' : 
                                  reading.month == 5 ? 'May' : 
                                  reading.month == 6 ? 'June' : 
                                  reading.month == 7 ? 'July' : 
                                  reading.month == 8 ? 'August' : 
                                  reading.month == 9 ? 'September' : 
                                  reading.month == 10 ? 'October' : 
                                  reading.month == 11 ? 'November' : 'December'}"></td>
                    <td th:text="${reading.year}"></td>
                    <td th:text="${reading.createdAt}"></td>
                    <td th:text="${reading.modifiedAt}"></td>
                    <td th:text="${reading.waterReadings.size()}"></td>
                    <td>
                        <a th:href="@{/readings/{id}(id=${reading.id})}" class="btn btn-sm btn-info">View Details</a>
                    </td>
                </tr>
                <tr th:if="${#lists.isEmpty(readings)}">
                    <td colspan="7" class="text-center">No readings found</td>
                </tr>
            </tbody>
        </table>
    </div>
    <div th:replace="~{fragments/layout :: footer}"></div>
</body>
</html>
