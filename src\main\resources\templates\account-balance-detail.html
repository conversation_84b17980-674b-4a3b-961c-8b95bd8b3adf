<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:replace="~{fragments/layout :: head('Account Balance Detail')}">
</head>
<body>
    <div th:replace="~{fragments/layout :: header}"></div>
    
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Account Balance Detail</h1>
            <div>
                <a th:href="@{/account-balances}" class="btn btn-secondary">Back to Account Balances</a>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    Account Balance for 
                    <span th:text="${accountBalance.month == 1 ? 'January' : 
                                    accountBalance.month == 2 ? 'February' : 
                                    accountBalance.month == 3 ? 'March' : 
                                    accountBalance.month == 4 ? 'April' : 
                                    accountBalance.month == 5 ? 'May' : 
                                    accountBalance.month == 6 ? 'June' : 
                                    accountBalance.month == 7 ? 'July' : 
                                    accountBalance.month == 8 ? 'August' : 
                                    accountBalance.month == 9 ? 'September' : 
                                    accountBalance.month == 10 ? 'October' : 
                                    accountBalance.month == 11 ? 'November' : 'December'}"></span>
                    <span th:text="${accountBalance.year}"></span>
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-bordered">
                            <tr>
                                <th>Previous Month Balance</th>
                                <td th:text="${'Rs.' + #numbers.formatDecimal(accountBalance.previousMonthBalance, 1, 'COMMA', 2, 'POINT')}"></td>
                            </tr>
                            <tr>
                                <th>Payments Received</th>
                                <td th:text="${'Rs.' + #numbers.formatDecimal(accountBalance.payments, 1, 'COMMA', 2, 'POINT')}"></td>
                            </tr>
                            <tr>
                                <th>Credits</th>
                                <td th:text="${'Rs.' + #numbers.formatDecimal(accountBalance.credits, 1, 'COMMA', 2, 'POINT')}"></td>
                            </tr>
                            <tr>
                                <th>Expenses</th>
                                <td th:text="${'Rs.' + #numbers.formatDecimal(accountBalance.expenses, 1, 'COMMA', 2, 'POINT')}"></td>
                            </tr>
                            <tr class="table-primary">
                                <th>Current Balance</th>
                                <td th:text="${'Rs.' + #numbers.formatDecimal(accountBalance.currentBalance, 1, 'COMMA', 2, 'POINT')}"></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-header">Balance Calculation</div>
                            <div class="card-body">
                                <p>Previous Balance: <span th:text="${'Rs.' + #numbers.formatDecimal(accountBalance.previousMonthBalance, 1, 'COMMA', 2, 'POINT')}"></span></p>
                                <p>+ Payments: <span th:text="${'Rs.' + #numbers.formatDecimal(accountBalance.payments, 1, 'COMMA', 2, 'POINT')}"></span></p>
                                <p>+ Credits: <span th:text="${'Rs.' + #numbers.formatDecimal(accountBalance.credits, 1, 'COMMA', 2, 'POINT')}"></span></p>
                                <p>- Expenses: <span th:text="${'Rs.' + #numbers.formatDecimal(accountBalance.expenses, 1, 'COMMA', 2, 'POINT')}"></span></p>
                                <hr>
                                <p class="fw-bold">= Current Balance: <span th:text="${'Rs.' + #numbers.formatDecimal(accountBalance.currentBalance, 1, 'COMMA', 2, 'POINT')}"></span></p>
                                
                                <div class="mt-3">
                                    <p><strong>Last Updated:</strong> <span th:text="${#temporals.format(accountBalance.updatedOn, 'dd-MM-yyyy HH:mm')}"></span></p>
                                    <p><strong>Created On:</strong> <span th:text="${#temporals.format(accountBalance.createdOn, 'dd-MM-yyyy HH:mm')}"></span></p>
                                </div>
                                
                                <div class="mt-3">
                                    <a th:if="${accountBalance.recalculate}" 
                                       th:href="@{/account-balances/recalculate/{id}(id=${accountBalance.id})}" 
                                       class="btn btn-warning"
                                       onclick="return confirm('Are you sure you want to recalculate this balance?')">
                                        Recalculate Balance
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-success text-white">Payments</div>
                    <div class="card-body">
                        <h3 th:text="${'Rs.' + #numbers.formatDecimal(accountBalance.payments, 1, 'COMMA', 2, 'POINT')}"></h3>
                        <a th:href="@{/payments(month=${accountBalance.month}, year=${accountBalance.year})}" class="btn btn-outline-success">View Payments</a>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-info text-white">Credits</div>
                    <div class="card-body">
                        <h3 th:text="${'Rs.' + #numbers.formatDecimal(accountBalance.credits, 1, 'COMMA', 2, 'POINT')}"></h3>
                        <a th:href="@{/credits(month=${accountBalance.month}, year=${accountBalance.year})}" class="btn btn-outline-info">View Credits</a>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-danger text-white">Expenses</div>
                    <div class="card-body">
                        <h3 th:text="${'Rs.' + #numbers.formatDecimal(accountBalance.expenses, 1, 'COMMA', 2, 'POINT')}"></h3>
                        <a th:href="@{/expenses(month=${accountBalance.month}, year=${accountBalance.year})}" class="btn btn-outline-danger">View Expenses</a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">Balance History</div>
            <div class="card-body">
                <p>This section could display a history of balance changes or related transactions.</p>
                
                <!-- Placeholder for future implementation -->
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> Detailed transaction history will be available in a future update.
                </div>
            </div>
        </div>
    </div>
    
    <div th:replace="~{fragments/layout :: footer}"></div>
</body>
</html>