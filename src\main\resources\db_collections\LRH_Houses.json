[{"_id": 100100, "houseNumber": 1, "ownerName": "<PERSON><PERSON><PERSON>", "residentName": "<PERSON><PERSON><PERSON>", "balance": 40254, "area": 240, "rate": 2338, "garbage": 0, "previousWaterMeterReading": 1482, "currentWaterMeterReading": 1482}, {"_id": 100200, "houseNumber": 2, "ownerName": "<PERSON>", "residentName": "<PERSON>", "balance": 0, "area": 240, "rate": 2338, "garbage": 61, "previousWaterMeterReading": 1798, "currentWaterMeterReading": 1798}, {"_id": 100300, "houseNumber": 3, "ownerName": "<PERSON><PERSON><PERSON>", "residentName": "<PERSON><PERSON><PERSON>", "balance": 12985, "area": 240, "rate": 2338, "garbage": 0, "previousWaterMeterReading": 2040, "currentWaterMeterReading": 2040}, {"_id": 100400, "houseNumber": 4, "ownerName": "<PERSON><PERSON><PERSON>", "residentName": "<PERSON><PERSON><PERSON>", "balance": 0, "area": 240, "rate": 2338, "garbage": 61, "previousWaterMeterReading": 1003, "currentWaterMeterReading": 1003}, {"_id": 100500, "houseNumber": 5, "ownerName": "<PERSON><PERSON><PERSON>", "residentName": "<PERSON><PERSON><PERSON>", "balance": 110030, "area": 240, "rate": 2285, "garbage": 61, "previousWaterMeterReading": 0, "currentWaterMeterReading": 0}, {"_id": 100600, "houseNumber": 6, "ownerName": "<PERSON><PERSON><PERSON>", "residentName": "<PERSON><PERSON><PERSON>", "balance": 0, "area": 240, "rate": 2338, "garbage": 61, "previousWaterMeterReading": 3183, "currentWaterMeterReading": 3183}, {"_id": 100700, "houseNumber": 7, "ownerName": "<PERSON><PERSON><PERSON>", "residentName": "<PERSON><PERSON><PERSON>", "balance": 0, "area": 240, "rate": 2374, "garbage": 61, "previousWaterMeterReading": 660, "currentWaterMeterReading": 660}, {"_id": 100800, "houseNumber": 8, "ownerName": "<PERSON><PERSON>", "residentName": "<PERSON><PERSON>", "balance": 0, "area": 240, "rate": 2338, "garbage": 61, "previousWaterMeterReading": 1075, "currentWaterMeterReading": 1075}, {"_id": 100900, "houseNumber": 9, "ownerName": "<PERSON><PERSON><PERSON>", "residentName": "<PERSON><PERSON><PERSON>", "balance": 0, "area": 240, "rate": 2374, "garbage": 61, "previousWaterMeterReading": 1134, "currentWaterMeterReading": 1134}, {"_id": 101000, "houseNumber": 10, "ownerName": "Moorthy VKNSN", "residentName": "Moorthy VKNSN", "balance": 3305, "area": 240, "rate": 2374, "garbage": 61, "previousWaterMeterReading": 2573, "currentWaterMeterReading": 2573}, {"_id": 101100, "houseNumber": 11, "ownerName": "<PERSON> <PERSON>", "residentName": "<PERSON> <PERSON>", "balance": 6170, "area": 240, "rate": 2374, "garbage": 61, "previousWaterMeterReading": 267, "currentWaterMeterReading": 267}, {"_id": 101200, "houseNumber": 12, "ownerName": "<PERSON>", "residentName": "<PERSON>", "balance": 0, "area": 240, "rate": 2374, "garbage": 61, "previousWaterMeterReading": 190, "currentWaterMeterReading": 190}, {"_id": 101300, "houseNumber": 13, "ownerName": "<PERSON>", "residentName": "<PERSON>", "balance": 2634, "area": 240, "rate": 2374, "garbage": 0, "previousWaterMeterReading": 1275, "currentWaterMeterReading": 1275}, {"_id": 101400, "houseNumber": 14, "ownerName": "<PERSON>", "residentName": "<PERSON>", "balance": 2374, "area": 240, "rate": 2374, "garbage": 0, "previousWaterMeterReading": 0, "currentWaterMeterReading": 0}, {"_id": 101500, "houseNumber": 15, "ownerName": "<PERSON><PERSON><PERSON>", "residentName": "<PERSON><PERSON><PERSON>", "balance": 0, "area": 200, "rate": 2020, "garbage": 61, "previousWaterMeterReading": 742, "currentWaterMeterReading": 742}, {"_id": 101600, "houseNumber": 16, "ownerName": "<PERSON><PERSON><PERSON>", "residentName": "<PERSON><PERSON><PERSON>", "balance": 7822, "area": 200, "rate": 2020, "garbage": 61, "previousWaterMeterReading": 3257, "currentWaterMeterReading": 3257}, {"_id": 101700, "houseNumber": 17, "ownerName": "<PERSON>  <PERSON>", "residentName": "<PERSON>  <PERSON>", "balance": 0, "area": 200, "rate": 2020, "garbage": 61, "previousWaterMeterReading": 2859, "currentWaterMeterReading": 2859}, {"_id": 101800, "houseNumber": 18, "ownerName": "<PERSON><PERSON><PERSON>", "residentName": "<PERSON><PERSON><PERSON>", "balance": 0, "area": 200, "rate": 2020, "garbage": 61, "previousWaterMeterReading": 3744, "currentWaterMeterReading": 3744}, {"_id": 101900, "houseNumber": 19, "ownerName": "<PERSON>", "residentName": "<PERSON>", "balance": 0, "area": 200, "rate": 2020, "garbage": 61, "previousWaterMeterReading": 1284, "currentWaterMeterReading": 1284}, {"_id": 102000, "houseNumber": 20, "ownerName": "<PERSON><PERSON><PERSON><PERSON>", "residentName": "<PERSON><PERSON><PERSON><PERSON>", "balance": 0, "area": 200, "rate": 2020, "garbage": 61, "previousWaterMeterReading": 190, "currentWaterMeterReading": 190}, {"_id": 102100, "houseNumber": 21, "ownerName": "<PERSON><PERSON>", "residentName": "<PERSON><PERSON>", "balance": 5739, "area": 200, "rate": 2020, "garbage": 61, "previousWaterMeterReading": 4110, "currentWaterMeterReading": 4110}, {"_id": 102200, "houseNumber": 22, "ownerName": "<PERSON><PERSON><PERSON>", "residentName": "<PERSON><PERSON><PERSON>", "balance": 2786, "area": 200, "rate": 2020, "garbage": 61, "previousWaterMeterReading": 3355, "currentWaterMeterReading": 3355}, {"_id": 102300, "houseNumber": 23, "ownerName": "<PERSON><PERSON><PERSON>", "residentName": "<PERSON><PERSON><PERSON>", "balance": 0, "area": 200, "rate": 2020, "garbage": 61, "previousWaterMeterReading": 2237, "currentWaterMeterReading": 2237}, {"_id": 102400, "houseNumber": 24, "ownerName": "Pandu Ra<PERSON>", "residentName": "Pandu Ra<PERSON>", "balance": 235, "area": 200, "rate": 2020, "garbage": 61, "previousWaterMeterReading": 1464, "currentWaterMeterReading": 1464}, {"_id": 102500, "houseNumber": 25, "ownerName": "<PERSON><PERSON><PERSON>", "residentName": "<PERSON><PERSON><PERSON>", "balance": 4444, "area": 200, "rate": 2020, "garbage": 61, "previousWaterMeterReading": 2770, "currentWaterMeterReading": 2770}, {"_id": 102600, "houseNumber": 26, "ownerName": "<PERSON><PERSON><PERSON>", "residentName": "<PERSON><PERSON><PERSON>", "balance": 0, "area": 200, "rate": 2020, "garbage": 61, "previousWaterMeterReading": 2071, "currentWaterMeterReading": 2071}, {"_id": 102700, "houseNumber": 27, "ownerName": "<PERSON><PERSON>", "residentName": "<PERSON><PERSON>", "balance": 2020, "area": 200, "rate": 2020, "garbage": 0, "previousWaterMeterReading": 551, "currentWaterMeterReading": 551}, {"_id": 102800, "houseNumber": 28, "ownerName": "<PERSON><PERSON>", "residentName": "<PERSON><PERSON>", "balance": 0, "area": 200, "rate": 2020, "garbage": 61, "previousWaterMeterReading": 3455, "currentWaterMeterReading": 3455}, {"_id": 102900, "houseNumber": 29, "ownerName": "<PERSON> <PERSON><PERSON>", "residentName": "<PERSON> <PERSON><PERSON>", "balance": 4130, "area": 200, "rate": 2020, "garbage": 0, "previousWaterMeterReading": 2631, "currentWaterMeterReading": 2631}, {"_id": 103000, "houseNumber": 30, "ownerName": "<PERSON><PERSON><PERSON>", "residentName": "<PERSON><PERSON><PERSON>", "balance": 7680, "area": 200, "rate": 2020, "garbage": 61, "previousWaterMeterReading": 109, "currentWaterMeterReading": 109}, {"_id": 103100, "houseNumber": 31, "ownerName": "<PERSON><PERSON><PERSON>", "residentName": "<PERSON><PERSON><PERSON>", "balance": 0, "area": 200, "rate": 2020, "garbage": 61, "previousWaterMeterReading": 3953, "currentWaterMeterReading": 3953}, {"_id": 103200, "houseNumber": 32, "ownerName": "<PERSON><PERSON><PERSON>", "residentName": "<PERSON><PERSON><PERSON>", "balance": -379, "area": 200, "rate": 2020, "garbage": 61, "previousWaterMeterReading": 3020, "currentWaterMeterReading": 3020}, {"_id": 103300, "houseNumber": 33, "ownerName": "Not Known", "residentName": "Not Known", "balance": 169630, "area": 200, "rate": 2020, "garbage": 0, "previousWaterMeterReading": 0, "currentWaterMeterReading": 0}, {"_id": 103400, "houseNumber": 34, "ownerName": "<PERSON><PERSON><PERSON><PERSON>", "residentName": "<PERSON><PERSON><PERSON><PERSON>", "balance": 10774, "area": 200, "rate": 2020, "garbage": 61, "previousWaterMeterReading": 3424, "currentWaterMeterReading": 3424}, {"_id": 103500, "houseNumber": 35, "ownerName": "<PERSON><PERSON><PERSON>", "residentName": "<PERSON><PERSON><PERSON>", "balance": 322, "area": 200, "rate": 2020, "garbage": 61, "previousWaterMeterReading": 3190, "currentWaterMeterReading": 3190}, {"_id": 103600, "houseNumber": 36, "ownerName": "Not Known", "residentName": "Not Known", "balance": 169630, "area": 200, "rate": 2020, "garbage": 0, "previousWaterMeterReading": 0, "currentWaterMeterReading": 0}, {"_id": 103700, "houseNumber": 37, "ownerName": "Not Known", "residentName": "Not Known", "balance": 169630, "area": 200, "rate": 2020, "garbage": 0, "previousWaterMeterReading": 0, "currentWaterMeterReading": 0}, {"_id": 103800, "houseNumber": 38, "ownerName": "Not Known", "residentName": "Not Known", "balance": 169630, "area": 200, "rate": 2020, "garbage": 0, "previousWaterMeterReading": 0, "currentWaterMeterReading": 0}, {"_id": 103900, "houseNumber": 39, "ownerName": "<PERSON><PERSON><PERSON>", "residentName": "<PERSON><PERSON><PERSON>", "balance": 77036, "area": 200, "rate": 2020, "garbage": 0, "previousWaterMeterReading": 0, "currentWaterMeterReading": 0}, {"_id": 104000, "houseNumber": 40, "ownerName": "Paritala Venkata Ramana", "residentName": "Paritala Venkata Ramana", "balance": 0, "area": 200, "rate": 2020, "garbage": 61, "previousWaterMeterReading": 2158, "currentWaterMeterReading": 2158}, {"_id": 104100, "houseNumber": 41, "ownerName": "<PERSON> <PERSON><PERSON>", "residentName": "<PERSON> <PERSON><PERSON>", "balance": 3922, "area": 200, "rate": 2020, "garbage": 61, "previousWaterMeterReading": 683, "currentWaterMeterReading": 683}, {"_id": 104200, "houseNumber": 42, "ownerName": "Not Known", "residentName": "Not Known", "balance": 169630, "area": 200, "rate": 2020, "garbage": 0, "previousWaterMeterReading": 0, "currentWaterMeterReading": 0}, {"_id": 104300, "houseNumber": 43, "ownerName": "Not Known", "residentName": "Not Known", "balance": 169630, "area": 200, "rate": 2020, "garbage": 0, "previousWaterMeterReading": 0, "currentWaterMeterReading": 0}, {"_id": 104400, "houseNumber": 44, "ownerName": "Ashok B", "residentName": "Ashok B", "balance": 13406, "area": 200, "rate": 2020, "garbage": 0, "previousWaterMeterReading": 1566, "currentWaterMeterReading": 1566}, {"_id": 104500, "houseNumber": 45, "ownerName": "<PERSON><PERSON><PERSON><PERSON>", "residentName": "<PERSON><PERSON><PERSON><PERSON>", "balance": 2621, "area": 200, "rate": 2020, "garbage": 61, "previousWaterMeterReading": 2681, "currentWaterMeterReading": 2681}, {"_id": 104600, "houseNumber": 46, "ownerName": "<PERSON>", "residentName": "<PERSON>", "balance": 4721, "area": 200, "rate": 2020, "garbage": 61, "previousWaterMeterReading": 5223, "currentWaterMeterReading": 5223}, {"_id": 104700, "houseNumber": 47, "ownerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "residentName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "balance": 15693, "area": 200, "rate": 2020, "garbage": 61, "previousWaterMeterReading": 1154, "currentWaterMeterReading": 1154}, {"_id": 104800, "houseNumber": 48, "ownerName": "<PERSON><PERSON><PERSON><PERSON>", "residentName": "<PERSON><PERSON><PERSON><PERSON>", "balance": -636, "area": 200, "rate": 2020, "garbage": 61, "previousWaterMeterReading": 2148, "currentWaterMeterReading": 2148}, {"_id": 104900, "houseNumber": 49, "ownerName": "Areti Venkata Srinivas", "residentName": "Areti Venkata Srinivas", "balance": 0, "area": 200, "rate": 2020, "garbage": 61, "previousWaterMeterReading": 3437, "currentWaterMeterReading": 3437}, {"_id": 105000, "houseNumber": 50, "ownerName": "<PERSON><PERSON><PERSON>", "residentName": "<PERSON><PERSON><PERSON>", "balance": -389, "area": 200, "rate": 2020, "garbage": 61, "previousWaterMeterReading": 2553, "currentWaterMeterReading": 2553}, {"_id": 105100, "houseNumber": 51, "ownerName": "<PERSON><PERSON><PERSON>", "residentName": "<PERSON><PERSON><PERSON>", "balance": 12024, "area": 200, "rate": 2020, "garbage": 61, "previousWaterMeterReading": 2005, "currentWaterMeterReading": 2005}, {"_id": 105200, "houseNumber": 52, "ownerName": "<PERSON>", "residentName": "<PERSON>", "balance": 0, "area": 200, "rate": 2020, "garbage": 61, "previousWaterMeterReading": 1867, "currentWaterMeterReading": 1867}, {"_id": 105300, "houseNumber": 53, "ownerName": "<PERSON>", "residentName": "<PERSON>", "balance": 10697, "area": 200, "rate": 2020, "garbage": 61, "previousWaterMeterReading": 1230, "currentWaterMeterReading": 1230}, {"_id": 105400, "houseNumber": 54, "ownerName": "<PERSON><PERSON><PERSON><PERSON>", "residentName": "<PERSON><PERSON><PERSON><PERSON>", "balance": 100, "area": 200, "rate": 2020, "garbage": 61, "previousWaterMeterReading": 3110, "currentWaterMeterReading": 3110}, {"_id": 105500, "houseNumber": 55, "ownerName": "<PERSON><PERSON><PERSON><PERSON>", "residentName": "<PERSON><PERSON><PERSON><PERSON>", "balance": 2281, "area": 200, "rate": 2020, "garbage": 61, "previousWaterMeterReading": 10, "currentWaterMeterReading": 10}, {"_id": 105600, "houseNumber": 56, "ownerName": "<PERSON><PERSON><PERSON><PERSON>", "residentName": "<PERSON><PERSON><PERSON><PERSON>", "balance": 2661, "area": 200, "rate": 2020, "garbage": 61, "previousWaterMeterReading": 2749, "currentWaterMeterReading": 2749}, {"_id": 105700, "houseNumber": 57, "ownerName": "Y V V <PERSON>", "residentName": "Y V V <PERSON>", "balance": 0, "area": 200, "rate": 1648, "garbage": 61, "previousWaterMeterReading": 700, "currentWaterMeterReading": 700}, {"_id": 105800, "houseNumber": 58, "ownerName": "K N K Raamesh Sarma", "residentName": "K N K Raamesh Sarma", "balance": 8036, "area": 200, "rate": 1648, "garbage": 61, "previousWaterMeterReading": 646, "currentWaterMeterReading": 646}, {"_id": 105900, "houseNumber": "59A", "ownerName": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "residentName": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "balance": 0, "area": 200, "rate": 1427, "garbage": 61, "previousWaterMeterReading": 10, "currentWaterMeterReading": 10}, {"_id": 105901, "houseNumber": 59, "ownerName": "<PERSON><PERSON><PERSON><PERSON>", "residentName": "<PERSON><PERSON><PERSON><PERSON>", "balance": 0, "area": 200, "rate": 1577, "garbage": 61, "previousWaterMeterReading": 63, "currentWaterMeterReading": 63}, {"_id": 106000, "houseNumber": 60, "ownerName": "<PERSON>", "residentName": "<PERSON>", "balance": 35660, "area": 200, "rate": 2020, "garbage": 0, "previousWaterMeterReading": 0, "currentWaterMeterReading": 0}, {"_id": 106100, "houseNumber": 61, "ownerName": "Not Known", "residentName": "Not Known", "balance": 169630, "area": 200, "rate": 2020, "garbage": 0, "previousWaterMeterReading": 0, "currentWaterMeterReading": 0}, {"_id": 106200, "houseNumber": 62, "ownerName": "Not Known", "residentName": "Not Known", "balance": 169630, "area": 200, "rate": 2020, "garbage": 0, "previousWaterMeterReading": 0, "currentWaterMeterReading": 0}, {"_id": 106300, "houseNumber": 63, "ownerName": "Not Known", "residentName": "Not Known", "balance": 169630, "area": 200, "rate": 2020, "garbage": 0, "previousWaterMeterReading": 0, "currentWaterMeterReading": 0}, {"_id": 106400, "houseNumber": 64, "ownerName": "<PERSON><PERSON>", "residentName": "<PERSON><PERSON>", "balance": 0, "area": 200, "rate": 2020, "garbage": 61, "previousWaterMeterReading": 2376, "currentWaterMeterReading": 2376}, {"_id": 106500, "houseNumber": 65, "ownerName": "B Srikanth", "residentName": "B Srikanth", "balance": 0, "area": 200, "rate": 2020, "garbage": 61, "previousWaterMeterReading": 575, "currentWaterMeterReading": 575}, {"_id": 106600, "houseNumber": 66, "ownerName": "S Pavan", "residentName": "S Pavan", "balance": 0, "area": 200, "rate": 2020, "garbage": 61, "previousWaterMeterReading": 1873, "currentWaterMeterReading": 1873}, {"_id": 106700, "houseNumber": 67, "ownerName": "<PERSON><PERSON><PERSON>", "residentName": "<PERSON><PERSON><PERSON>", "balance": 0, "area": 200, "rate": 2020, "garbage": 61, "previousWaterMeterReading": 1507, "currentWaterMeterReading": 1507}, {"_id": 106800, "houseNumber": 68, "ownerName": "<PERSON><PERSON><PERSON>", "residentName": "<PERSON><PERSON><PERSON>", "balance": 169630, "area": 200, "rate": 2020, "garbage": 0, "previousWaterMeterReading": 0, "currentWaterMeterReading": 0}, {"_id": 106900, "houseNumber": 69, "ownerName": "M <PERSON>", "residentName": "M <PERSON>", "balance": 0, "area": 200, "rate": 2020, "garbage": 0, "previousWaterMeterReading": 689, "currentWaterMeterReading": 689}, {"_id": 107001, "houseNumber": "70A", "ownerName": "<PERSON><PERSON><PERSON>", "residentName": "<PERSON><PERSON><PERSON>", "balance": 2238, "area": 200, "rate": 1557, "garbage": 61, "previousWaterMeterReading": 1279, "currentWaterMeterReading": 1279}, {"_id": 107000, "houseNumber": 70, "ownerName": "<PERSON>", "residentName": "<PERSON>", "balance": 118, "area": 200, "rate": 1577, "garbage": 61, "previousWaterMeterReading": 974, "currentWaterMeterReading": 974}, {"_id": 107100, "houseNumber": 71, "ownerName": "M <PERSON>", "residentName": "M <PERSON>", "balance": 0, "area": 200, "rate": 1577, "garbage": 61, "previousWaterMeterReading": 39, "currentWaterMeterReading": 39}, {"_id": 107200, "houseNumber": 72, "ownerName": "<PERSON>  <PERSON>", "residentName": "<PERSON>  <PERSON>", "balance": 2038, "area": 200, "rate": 1577, "garbage": 61, "previousWaterMeterReading": 840, "currentWaterMeterReading": 840}, {"_id": 107300, "houseNumber": 73, "ownerName": "Chittha Reddy", "residentName": "Chittha Reddy", "balance": 8843, "area": 200, "rate": 2020, "garbage": 61, "previousWaterMeterReading": 26, "currentWaterMeterReading": 26}, {"_id": 107400, "houseNumber": 74, "ownerName": "<PERSON>", "residentName": "<PERSON>", "balance": 2557, "area": 200, "rate": 2020, "garbage": 61, "previousWaterMeterReading": 3150, "currentWaterMeterReading": 3150}, {"_id": 107700, "houseNumber": 77, "ownerName": "<PERSON><PERSON>", "residentName": "<PERSON><PERSON>", "balance": 750, "area": 200, "rate": 1489, "garbage": 61, "previousWaterMeterReading": 137, "currentWaterMeterReading": 137}, {"_id": 107800, "houseNumber": 78, "ownerName": "<PERSON><PERSON><PERSON><PERSON>", "residentName": "<PERSON><PERSON><PERSON><PERSON>", "balance": 0, "area": 200, "rate": 2391, "garbage": 61, "previousWaterMeterReading": 127, "currentWaterMeterReading": 127}, {"_id": 107900, "houseNumber": 79, "ownerName": "A Ashok", "residentName": "A Ashok", "balance": 3842, "area": 200, "rate": 1480, "garbage": 61, "previousWaterMeterReading": 588, "currentWaterMeterReading": 588}, {"_id": 108000, "houseNumber": 80, "ownerName": "S Prabhakar", "residentName": "S Prabhakar", "balance": 6888, "area": 200, "rate": 1471, "garbage": 61, "previousWaterMeterReading": 507, "currentWaterMeterReading": 507}, {"_id": 108100, "houseNumber": 81, "ownerName": "G Satish", "residentName": "G Satish", "balance": 7308, "area": 200, "rate": 1471, "garbage": 61, "previousWaterMeterReading": 413, "currentWaterMeterReading": 413}, {"_id": 108200, "houseNumber": 82, "ownerName": "<PERSON><PERSON>", "residentName": "<PERSON><PERSON>", "balance": 3124, "area": 200, "rate": 2073, "garbage": 61, "previousWaterMeterReading": 781, "currentWaterMeterReading": 781}, {"_id": 108300, "houseNumber": 83, "ownerName": "<PERSON><PERSON><PERSON>", "residentName": "<PERSON><PERSON><PERSON>", "balance": 0, "area": 200, "rate": 2073, "garbage": 61, "previousWaterMeterReading": 485, "currentWaterMeterReading": 485}]