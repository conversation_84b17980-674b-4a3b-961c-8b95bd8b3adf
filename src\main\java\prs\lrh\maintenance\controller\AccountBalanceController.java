package prs.lrh.maintenance.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import com.lowagie.text.Document;
import com.lowagie.text.DocumentException;
import com.lowagie.text.Element;
import com.lowagie.text.Font;
import com.lowagie.text.FontFactory;
import com.lowagie.text.Image;
import com.lowagie.text.Paragraph;
import com.lowagie.text.Phrase;
import com.lowagie.text.pdf.PdfContentByte;
import com.lowagie.text.pdf.PdfPCell;
import com.lowagie.text.pdf.PdfPTable;
import com.lowagie.text.pdf.PdfPageEventHelper;
import com.lowagie.text.pdf.PdfWriter;

import prs.lrh.maintenance.model.AccountBalance;
import prs.lrh.maintenance.repository.AccountBalanceRepository;
import prs.lrh.maintenance.model.Payment;
import prs.lrh.maintenance.model.ExpenseRecord;
import prs.lrh.maintenance.model.ExpenseItem;
import prs.lrh.maintenance.model.CreditRecord;
import prs.lrh.maintenance.model.CreditItem;
import prs.lrh.maintenance.repository.ExpenseRepository;
import prs.lrh.maintenance.repository.CreditRepository;
import prs.lrh.maintenance.service.MongoDBService;

import java.awt.Color;
import java.io.ByteArrayOutputStream;
import java.time.LocalDateTime;
import java.time.Month;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.List;
import java.util.Locale;
import java.util.Optional;

@Controller
@RequestMapping("/account-balances")
public class AccountBalanceController {

    private static final Logger logger = LoggerFactory.getLogger(AccountBalanceController.class);
    
    @Autowired
    private AccountBalanceRepository accountBalanceRepository;
    
    @Autowired
    private ExpenseRepository expenseRepository;

    @Autowired
    private CreditRepository creditRepository;

    @Autowired
    private MongoDBService mongoDBService;
    
    @GetMapping
    public String listAccountBalances(@RequestParam(required = false) Integer year, Model model) {
        
        List<AccountBalance> accountBalances;
        boolean showCreateButton = false;
        
        // If year is provided
        if (year != null) {
            accountBalances = accountBalanceRepository.findByYear(year);
        } 
        // If no filters are provided
        else {
            accountBalances = accountBalanceRepository.findByOrderByYearDescMonthDesc();
        }
        
        // Check if current month's balance exists
        YearMonth currentYearMonth = YearMonth.now();
        int currentMonth = currentYearMonth.getMonthValue();
        int currentYear = currentYearMonth.getYear();
        
        Optional<AccountBalance> currentMonthBalance = accountBalanceRepository.findByMonthAndYear(currentMonth, currentYear);
        if (currentMonthBalance.isEmpty()) {
            showCreateButton = true;
        }
        
        logger.info("Found {} account balance records", accountBalances.size());
        model.addAttribute("accountBalances", accountBalances);
        model.addAttribute("currentYear", currentYear);
        model.addAttribute("showCreateButton", showCreateButton);
        
        // Calculate totals for the displayed records
        double totalPayments = accountBalances.stream().mapToDouble(AccountBalance::getPayments).sum();
        double totalCredits = accountBalances.stream().mapToDouble(AccountBalance::getCredits).sum();
        double totalExpenses = accountBalances.stream().mapToDouble(AccountBalance::getExpenses).sum();
        
        model.addAttribute("totalPayments", totalPayments);
        model.addAttribute("totalCredits", totalCredits);
        model.addAttribute("totalExpenses", totalExpenses);
        
        // Get the most recent balance
        if (!accountBalances.isEmpty()) {
            model.addAttribute("currentBalance", accountBalances.get(0).getCurrentBalance());
        } else {
            model.addAttribute("currentBalance", 0.0);
        }
        
        return "account-balances";
    }
    
    @GetMapping("/{id}")
    public String viewAccountBalance(@PathVariable String id, Model model) {
        logger.debug("Viewing account balance record with id: {}", id);
        Optional<AccountBalance> accountBalanceOpt = accountBalanceRepository.findById(id);
        
        if (accountBalanceOpt.isPresent()) {
            AccountBalance accountBalance = accountBalanceOpt.get();
            model.addAttribute("accountBalance", accountBalance);
            return "account-balance-detail";
        } else {
            return "redirect:/account-balances";
        }
    }
    
    @GetMapping("/recalculate/{id}")
    public String recalculateBalance(@PathVariable String id) {
        logger.debug("Recalculating account balance with id: {}", id);
        Optional<AccountBalance> accountBalanceOpt = accountBalanceRepository.findById(id);
        
        if (accountBalanceOpt.isPresent()) {
            recalculateAccountBalance(accountBalanceOpt);
            return "redirect:/account-balances/" + id;
        } else {
            return "redirect:/account-balances";
        }
    }

    @GetMapping("/generate-current")
    public String generateCurrentMonthBalance() {
        logger.info("Generating account balance for current month");
        
        // Get current month and year
        YearMonth currentYearMonth = YearMonth.now();
        int currentMonth = currentYearMonth.getMonthValue();
        int currentYear = currentYearMonth.getYear();
        
        // Check if balance already exists
        Optional<AccountBalance> existingBalance = accountBalanceRepository.findByMonthAndYear(currentMonth, currentYear);
        if (existingBalance.isPresent()) {
            logger.warn("Account balance for current month already exists");
            return "redirect:/account-balances/" + existingBalance.get().getId();
        }
        
        // Create new account balance
        AccountBalance newBalance = new AccountBalance();
        newBalance.setMonth(currentMonth);
        newBalance.setYear(currentYear);
        newBalance.setCreatedOn(LocalDateTime.now());
        newBalance.setUpdatedOn(LocalDateTime.now());
        newBalance.setRecalculate(true); // Set to true so it will be calculated
        
        // Save the new balance
        AccountBalance savedBalance = accountBalanceRepository.save(newBalance);
        
        // Recalculate the balance using existing logic
        Optional<AccountBalance> balanceToRecalculate = Optional.of(savedBalance);
        recalculateAccountBalance(balanceToRecalculate);
        
        return "redirect:/account-balances/" + savedBalance.getId();
    }
    
    // Extract the recalculation logic to a separate method for reuse
    private void recalculateAccountBalance(Optional<AccountBalance> accountBalanceOpt) {
        AccountBalance accountBalance = accountBalanceOpt.get();
        accountBalance.setRecalculate(true);
        accountBalance.setUpdatedOn(LocalDateTime.now());
        
        // Implement actual recalculation logic
        int month = accountBalance.getMonth();
        int year = accountBalance.getYear();
        
        // 1. Fetch previous month's account balance
        int prevMonth = month == 1 ? 12 : month - 1;
        int prevYear = month == 1 ? year - 1 : year;
        
        Optional<AccountBalance> prevMonthBalanceOpt = accountBalanceRepository.findByMonthAndYear(prevMonth, prevYear);
        double previousMonthBalance = prevMonthBalanceOpt.isPresent() ? 
            prevMonthBalanceOpt.get().getCurrentBalance() : 0.0;
        
        // 2. Fetch and sum all payments for the month
        List<Payment> payments = mongoDBService.findPaymentsByMonthAndYear(month, year);
        double totalPayments = payments.stream()
            .mapToDouble(Payment::getAmtPaid)
            .sum();
        
        // 3. Fetch and sum all expenses for the month
        Optional<ExpenseRecord> expenseOpt = expenseRepository.findByMonthAndYear(month, year);
        double totalExpenses = expenseOpt.map(expense -> 
            expense.getMonthlyExpenses().stream()
                .mapToDouble(ExpenseItem::getAmount)
                .sum()
        ).orElse(0.0);
        
        // 4. Fetch and sum all credits for the month
        Optional<CreditRecord> creditOpt = creditRepository.findByMonthAndYear(month, year);
        double totalCredits = creditOpt.map(credit -> 
            credit.getMonthlyCredits().stream()
                .mapToDouble(CreditItem::getAmount)
                .sum()
        ).orElse(0.0);
        
        // 5. Calculate current balance
        double currentBalance = previousMonthBalance + totalPayments + totalCredits - totalExpenses;
        
        // 6. Update the account balance
        accountBalance.setPreviousMonthBalance(previousMonthBalance);
        accountBalance.setPayments(totalPayments);
        accountBalance.setExpenses(totalExpenses);
        accountBalance.setCredits(totalCredits);
        accountBalance.setCurrentBalance(currentBalance);
        accountBalance.setUpdatedOn(LocalDateTime.now());
        
        accountBalanceRepository.save(accountBalance);
        logger.info("Successfully calculated balance for {}/{}", month, year);
    }

    @GetMapping("/pdf")
    @ResponseBody
    public ResponseEntity<byte[]> generateAccountBalancesPdf(
            @RequestParam(required = false) Integer year) throws DocumentException {
        
        List<AccountBalance> accountBalances;
        
        // If year is provided
        if (year != null) {
            accountBalances = accountBalanceRepository.findByYearOrderByMonthDesc(year);
        } 
        // If no filters are provided
        else {
            accountBalances = accountBalanceRepository.findByOrderByYearDescMonthDesc();
        }
        
               
        // Create PDF document
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        Document document = new Document();
        PdfWriter writer = PdfWriter.getInstance(document, baos);
        
        // Add page event handler for the logo
        writer.setPageEvent(new PdfPageEventHelper() {
            public void onEndPage(PdfWriter writer, Document document) {
                try {
                    // Load the logo image
                    Image logo = Image.getInstance(new ClassPathResource("static/images/lake_ridge_homes_header.jpg").getURL());
                    
                    // Scale the logo to fit about 50% of page width
                    float pageWidth = document.getPageSize().getWidth();
                    float logoWidth = pageWidth * 0.5f;
                    logo.scaleToFit(logoWidth, 60); // Maintain aspect ratio with max height 50
                    
                    // Position the logo at the top left corner of the page
                    // Add small margins (20 points from left, 20 points from top)                    
                    logo.setAbsolutePosition(35, document.getPageSize().getHeight() - 60);
                    
                    // Add the logo to the page
                    PdfContentByte cb = writer.getDirectContent();
                    cb.addImage(logo);
                } catch (Exception e) {
                    logger.error("Error adding logo to PDF: {}", e.getMessage());
                }
            }
        });
        
        document.open();

        // Add summary section
        Font summaryFont = FontFactory.getFont(FontFactory.HELVETICA, 6);
        Paragraph space = new Paragraph(".");
        space.setFont(summaryFont);
        space.setSpacingAfter(80); // Increased spacing from 100 to 120
        document.add(space);
        
        // Add title with spacing
        Font titleFont = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 16);
        Paragraph title = new Paragraph("Account Balances Report", titleFont);
        title.setAlignment(Element.ALIGN_CENTER);
        title.setSpacingAfter(40);
        document.add(title);
        // Add summary section

        Paragraph summary = new Paragraph("This report provides monthly account balances.");
        summary.setFont(summaryFont);
        summary.setSpacingAfter(20); // Increased spacing from 100 to 120
        document.add(summary);

        // Create table
        PdfPTable table = new PdfPTable(7); // 6 columns
        table.setWidthPercentage(100);
        
        // Set column widths
        float[] columnWidths = {1.0f, 1.0f, 1.5f, 1.0f, 1.5f, 1.5f, 1.5f};
        table.setWidths(columnWidths);
        
        // Add table headers
        Font headerFont = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 10, Color.WHITE);
        
        PdfPCell cell = new PdfPCell(new Phrase("Month", headerFont));
        cell.setBackgroundColor(new Color(66, 139, 202));
        cell.setPadding(5);
        table.addCell(cell);
        
        cell = new PdfPCell(new Phrase("Year", headerFont));
        cell.setBackgroundColor(new Color(66, 139, 202));
        cell.setPadding(5);
        table.addCell(cell);
        
        cell = new PdfPCell(new Phrase("Payments", headerFont));
        cell.setBackgroundColor(new Color(66, 139, 202));
        cell.setPadding(5);
        table.addCell(cell);
        
        cell = new PdfPCell(new Phrase("Credits", headerFont));
        cell.setBackgroundColor(new Color(66, 139, 202));
        cell.setPadding(5);
        table.addCell(cell);
        
        cell = new PdfPCell(new Phrase("Expenses", headerFont));
        cell.setBackgroundColor(new Color(66, 139, 202));
        cell.setPadding(5);
        table.addCell(cell);
        
        cell = new PdfPCell(new Phrase("Net Amount", headerFont));
        cell.setBackgroundColor(new Color(66, 139, 202));
        cell.setPadding(5);
        table.addCell(cell);
        
        cell = new PdfPCell(new Phrase("Balance", headerFont));
        cell.setBackgroundColor(new Color(66, 139, 202));
        cell.setPadding(5);
        table.addCell(cell);
        
        // Add table rows
        Font rowFont = FontFactory.getFont(FontFactory.HELVETICA, 9);
        Color lightGray = new Color(240, 240, 240);
        
        boolean alternate = false;
        for (AccountBalance balance : accountBalances) {
            // Month name
            String monthName = Month.of(balance.getMonth()).getDisplayName(TextStyle.FULL, Locale.ENGLISH);
            cell = new PdfPCell(new Phrase(monthName, rowFont));
            if (alternate) cell.setBackgroundColor(lightGray);
            cell.setPadding(5);
            table.addCell(cell);
            
            // Year
            cell = new PdfPCell(new Phrase(String.valueOf(balance.getYear()), rowFont));
            if (alternate) cell.setBackgroundColor(lightGray);
            cell.setPadding(5);
            table.addCell(cell);
            
            // Payments
            cell = new PdfPCell(new Phrase("Rs." + String.format("%,.2f", balance.getPayments()), rowFont));
            if (alternate) cell.setBackgroundColor(lightGray);
            cell.setPadding(5);
            cell.setHorizontalAlignment(Element.ALIGN_RIGHT);
            table.addCell(cell);
            
            // Credits
            cell = new PdfPCell(new Phrase("Rs." + String.format("%,.2f", balance.getCredits()), rowFont));
            if (alternate) cell.setBackgroundColor(lightGray);
            cell.setPadding(5);
            cell.setHorizontalAlignment(Element.ALIGN_RIGHT);
            table.addCell(cell);

            // Expenses
            cell = new PdfPCell(new Phrase("Rs." + String.format("%,.2f", balance.getExpenses()), rowFont));
            if (alternate) cell.setBackgroundColor(lightGray);
            cell.setPadding(5);
            cell.setHorizontalAlignment(Element.ALIGN_RIGHT);
            table.addCell(cell);

             // Net Amount = (Payments+Credits)-Expenses
            double netAmount =  (balance.getPayments() + balance.getCredits()) - balance.getExpenses();
            cell = new PdfPCell(new Phrase("Rs." + String.format("%,.2f", netAmount), rowFont));
            if (alternate) cell.setBackgroundColor(lightGray);
            cell.setPadding(5);
            cell.setHorizontalAlignment(Element.ALIGN_RIGHT);
            table.addCell(cell);
            
            // Current Balance
            cell = new PdfPCell(new Phrase("Rs." + String.format("%,.2f", balance.getCurrentBalance()), rowFont));
            if (alternate) cell.setBackgroundColor(lightGray);
            cell.setPadding(5);
            cell.setHorizontalAlignment(Element.ALIGN_RIGHT);
            table.addCell(cell);
            
            alternate = !alternate; // Toggle for alternating row colors
        }
        
        document.add(table);
        document.close();
        
        // Prepare response
        byte[] pdfBytes = baos.toByteArray();
        
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_PDF);
        httpHeaders.setContentDispositionFormData("attachment", 
                "Account_Balances_Report_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("dd-MM-yyyy_HH-mm")) + ".pdf");
        httpHeaders.setCacheControl("must-revalidate, post-check=0, pre-check=0");
        
        return ResponseEntity
                .ok()
                .headers(httpHeaders)
                .contentLength(pdfBytes.length)
                .body(pdfBytes);
    }
}

