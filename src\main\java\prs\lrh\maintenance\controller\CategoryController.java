package prs.lrh.maintenance.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import prs.lrh.maintenance.model.CategoryRecord;
import prs.lrh.maintenance.repository.CategoryRepository;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Controller
@RequestMapping("/categories")
public class CategoryController {
    
    private static final Logger logger = LoggerFactory.getLogger(CategoryController.class);
    private static final List<String> CTG_TYPES = Arrays.asList("EXPENSE_TYPE", "CREDIT_TYPE");
    
    @Autowired
    private CategoryRepository categoryRepository;
    
    @GetMapping
    public String listCategories(Model model, 
                               @RequestParam(required = false) String type) {
        
        List<CategoryRecord> categories;
        
        if (type != null && !type.isEmpty()) {
            categories = categoryRepository.findByTypeOrderByCategoryAsc(type);
            logger.info("Found {} categories of type: {}", categories.size(), type);
        } else {
            categories = categoryRepository.findAll();
            logger.info("Found {} total categories", categories.size());
        }
        
        model.addAttribute("categories", categories);
        model.addAttribute("types", CTG_TYPES);
        return "categories";
    }
    
    @GetMapping("/new")
    public String newCategoryForm(Model model) {
        model.addAttribute("categoryRecord", new CategoryRecord());
        model.addAttribute("types", CTG_TYPES);
        return "category-new";
    }
    
    @PostMapping("/add")
    public String addCategory(@RequestParam String category,
                            @RequestParam String type,
                            @RequestParam String subCategories) {
        
        logger.info("Adding new category: {}, type: {}", category, type);
        
        // Check if category already exists
        CategoryRecord existingCategory = categoryRepository.findByCategory(category);
        if (existingCategory != null) {
            logger.warn("Category already exists: {}", category);
            return "redirect:/categories?error=Category already exists";
        }
        
        // Parse subcategories from comma-separated string
        List<String> subCategoryList = Arrays.asList(subCategories.split(","));
        
        // Create new category record
        CategoryRecord newCategory = new CategoryRecord();
        newCategory.setCategory(category);
        newCategory.setType(type);
        newCategory.setSubCategory(subCategoryList);
        newCategory.setCreatedAt(LocalDateTime.now());
        newCategory.setModifiedAt(LocalDateTime.now());
        
        categoryRepository.save(newCategory);
        logger.info("Successfully added new category: {}", category);
        
        return "redirect:/categories";
    }
    
    @GetMapping("/{id}")
    public String viewCategory(@PathVariable String id, Model model) {
        Optional<CategoryRecord> category = categoryRepository.findById(id);
        
        if (category.isPresent()) {
            model.addAttribute("category", category.get());
            model.addAttribute("types", CTG_TYPES);
            return "category-detail";
        } else {
            return "redirect:/categories";
        }
    }
    
    @PostMapping("/update")
    public String updateCategory(@RequestParam String id,
                               @RequestParam String category,
                               @RequestParam String type,
                               @RequestParam String subCategories) {
        
        logger.info("Updating category with id: {}", id);
        
        Optional<CategoryRecord> existingCategory = categoryRepository.findById(id);
        
        if (existingCategory.isPresent()) {
            CategoryRecord categoryRecord = existingCategory.get();
            
            // Parse subcategories from comma-separated string
            List<String> subCategoryList = Arrays.asList(subCategories.split(","));
            
            categoryRecord.setCategory(category);
            categoryRecord.setType(type);
            categoryRecord.setSubCategory(subCategoryList);
            categoryRecord.setModifiedAt(LocalDateTime.now());
            
            categoryRepository.save(categoryRecord);
            logger.info("Successfully updated category: {}", category);
        } else {
            logger.warn("Category not found with id: {}", id);
        }
        
        return "redirect:/categories";
    }
    
    @GetMapping("/delete/{id}")
    public String deleteCategory(@PathVariable String id) {
        logger.info("Deleting category with id: {}", id);
        
        categoryRepository.deleteById(id);
        
        return "redirect:/categories";
    }
}