package prs.lrh.maintenance.repository;

import org.springframework.data.mongodb.repository.MongoRepository;
import prs.lrh.maintenance.model.MonthlyMaintenance;

import java.util.List;
import java.util.Optional;

public interface MonthlyMaintenanceRepository extends MongoRepository<MonthlyMaintenance, String> {
    List<MonthlyMaintenance> findByMonthAndYear(int month, int year);
    List<MonthlyMaintenance> findByYear(int year);
    List<MonthlyMaintenance> findByOrderByYearDescMonthDesc();
    Optional<MonthlyMaintenance> findFirstByMonthAndYear(int month, int year);
}