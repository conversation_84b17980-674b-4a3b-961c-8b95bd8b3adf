package prs.lrh.maintenance.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

@Data
@Document(collection = "account_balances")
public class AccountBalance {
    @Id
    private String id;
    private int month;
    private int year;
    private double previousMonthBalance;
    private double payments;
    private double credits;
    private double expenses;
    private double currentBalance;
    private boolean recalculate;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
}