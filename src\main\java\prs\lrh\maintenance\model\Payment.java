package prs.lrh.maintenance.model;

import lombok.Data;
import org.springframework.data.annotation.Id;

import java.time.LocalDateTime;

@Data
public class Payment {
    @Id
    private String id;
    private double amtPayable;
    private double amtPaid;
    private String comments;
    private int month;
    private int year;
    private LocalDateTime createdAt;
    private LocalDateTime modifiedAt;
}
