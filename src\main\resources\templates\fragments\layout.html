<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:fragment="head(title)">
    <meta charset="UTF-8">
    <title th:text="${title}">Lake Ridge Homes</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <style>
        .bg-light-blue {
            background-color: #e6ebff;
            margin: 0 2px;
            border-radius: 4px;
            padding: 8px 12px;
        }
        
        .bg-light-blue:hover {
            background-color: #cce5ff;
        }
        
        .text-end {
            text-align: right;
        }
        
        /* Style for the logout button to match other nav links */
        button.nav-link.bg-light-blue {
            background-color: #e6ebff;
            cursor: pointer;
            font-family: inherit;
            font-size: inherit;
            text-align: center;
        }
        
        button.nav-link.bg-light-blue:hover {
            background-color: #cce5ff;
        }
    </style>
</head>
<body>
    <div th:fragment="header">
        <div class="container">
            <div class="row mt-3 mb-3 align-items-center">
                <div class="col-md-8">
                    <a th:href="@{/}">
                        <img src="/images/lake_ridge_homes_header.jpg" alt="Lake Ridge Homes" class="img-fluid" style="max-height: 80px;">
                    </a>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex align-items-center justify-content-end">
                        <span class="me-2" th:text="${#authentication.principal.attributes.name}">User</span>
                        <img th:if="${#authentication.principal.attributes.picture}" 
                             th:src="${#authentication.principal.attributes.picture}" 
                             alt="Profile" class="rounded-circle" style="width: 40px; height: 40px;">
                    </div>
                </div>
            </div>
            <nav class="navbar navbar-expand-lg navbar-light bg-light mb-4">
                <div class="container-fluid">
                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                        <span class="navbar-toggler-icon"></span>
                    </button>
                    <div class="collapse navbar-collapse" id="navbarNav">
                        <ul class="navbar-nav">
                            <li class="nav-item">
                                <a class="nav-link bg-light-blue" th:href="@{/expenses}">Expenses</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link bg-light-blue" th:href="@{/houses}">Houses</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link bg-light-blue" th:href="@{/maintenance}">Maintenance</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link bg-light-blue" th:href="@{/readings}">Water-Readings</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link bg-light-blue" th:href="@{/reports/monthly}">Reports</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link bg-light-blue" th:href="@{/payments}">Payments</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link bg-light-blue" th:href="@{/credits}">Credits</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link bg-light-blue" th:href="@{/categories}">Categories</a>
                            </li>
                            <li class="nav-item">
                                <form th:action="@{/logout}" method="post" class="d-inline">
                                    <button type="submit" class="nav-link bg-light-blue border-0">Logout</button>
                                </form>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>
        </div>
    </div>

    <div th:fragment="footer">
        <div class="container mt-5">
            <hr>
            <p class="text-center text-muted">© 2025 Lake Ridge Homes</p>
        </div>
    </div>
</body>
</html>
