package prs.lrh.maintenance.config;

import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import com.mongodb.client.MongoDatabase;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;

/**
 * Singleton class to manage MongoDB client connection.
 * https://www.baeldung.com/java-mongodb#bd-1-maven-dependency
 * https://www.mongodb.com/docs/drivers/java/sync/current/crud/update-documents/
 */
@Component
public class MongoDBClient {
    private static MongoDBClient instance;
    private MongoClient mongoClient;
    
    @Value("${spring.data.mongodb.uri}")
    private String connectionString;
    
    @Value("${spring.data.mongodb.database}")
    private String databaseName;
    
    public MongoDBClient() {
        // Default constructor for Spring
    }
    
    @PostConstruct
    public void init() {
        try {
            mongoClient = MongoClients.create(connectionString);
            instance = this;
            // Test connection
            mongoClient.getDatabase(databaseName).listCollectionNames().first();
        } catch (Exception e) {
            throw new RuntimeException("Failed to initialize MongoDB connection: " + e.getMessage(), e);
        }
    }
    
    @PreDestroy
    public void cleanup() {
        if (mongoClient != null) {
            mongoClient.close();
        }
    }
    
    public static MongoDBClient getInstance() {
        if (instance == null) {
            throw new IllegalStateException("MongoDBClient has not been initialized yet");
        }
        return instance;
    }
    
    public MongoClient getMongoClient() {
        return mongoClient;
    }
    
    public MongoDatabase getDatabase() {
        return mongoClient.getDatabase(databaseName);
    }
}

