package prs.lrh.maintenance.model;

import lombok.Data;
import prs.lrh.maintenance.config.Role;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

@Data
@Document(collection = "users")
public class User {
    @Id
    private String id;
    private String name;
    private String email;
    private Role role;
    private boolean active;
    private LocalDateTime createdDate;
    private LocalDateTime lastLoginDate;
}
