spring:
  application:
    name: maintenance
  data:
    mongodb:
      uri: mongodb+srv://lrhadmin:<EMAIL>/
      database: maintenance
  security:
    oauth2:
      client:
        registration:
          google:
            client-id: 890549145824-e92qcsrfoqg37mc4l72emf08pu9dml5f.apps.googleusercontent.com
            client-secret: 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
            scope: profile,email
            redirect-uri: "{baseUrl}/login/oauth2/code/{registrationId}"