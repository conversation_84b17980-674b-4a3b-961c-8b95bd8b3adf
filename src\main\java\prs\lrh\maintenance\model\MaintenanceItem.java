package prs.lrh.maintenance.model;

import lombok.Data;

import java.util.List;

@Data
public class MaintenanceItem {
    private HouseRecord houseRecord;
    private MonthlyMaintenanceRecord monthlyMaintenanceRecord;
    private List<Payment> payments;
    private double totalAmtPaid;
    
    // Constructor for convenience
    public MaintenanceItem(HouseRecord houseRecord, MonthlyMaintenanceRecord monthlyMaintenanceRecord, List<Payment> payments) {
        this.houseRecord = houseRecord;
        this.monthlyMaintenanceRecord = monthlyMaintenanceRecord;
        this.payments = payments;
        this.totalAmtPaid = payments != null ? payments.stream().mapToDouble(Payment::getAmtPaid).sum() : 0.0;
    }
    
    // Default constructor
    public MaintenanceItem() {
    }
}
