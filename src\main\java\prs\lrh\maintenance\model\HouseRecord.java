package prs.lrh.maintenance.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document(collection = "house_records")
public class HouseRecord {
    @Id
    private Long id;
    private String houseNumber;
    private String ownerName;
    private String residentName;
    private double balance;
    private int area;
    private double rate;
    private double garbage;
    private int previousWaterMeterReading;
    private int currentWaterMeterReading;
}