package prs.lrh.maintenance.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Document(collection = "expense_records")
public class ExpenseRecord {
    @Id
    private String id;
    private int month;
    private int year;
    private List<ExpenseItem> monthlyExpenses;
    private LocalDateTime createdAt;
    private LocalDateTime modifiedAt;
}